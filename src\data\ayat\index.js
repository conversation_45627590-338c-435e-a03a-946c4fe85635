/**
 * Central Ayat manager - imports and manages all mood-based verses
 */

import sadAyat from './sad';
import happyAyat from './happy';
import angryAyat from './angry';
import peacefulAyat from './peaceful';
import { MOODS } from '../../contexts/MoodContext';

// Combine all ayat collections
export const ayatCollections = {
  [MOODS.SAD]: sadAyat,
  [MOODS.HAPPY]: happyAyat,
  [MOODS.ANGRY]: angryAyat,
  [MOODS.PEACEFUL]: peacefulAyat,
  [MOODS.NEUTRAL]: peacefulAyat // Use peaceful ayat for neutral mood
};

/**
 * Get a random ayah from a specific mood collection
 * @param {string} mood - The mood to get ayah for
 * @returns {object} Random ayah object
 */
export const getRandomAyah = (mood) => {
  const collection = ayatCollections[mood];
  if (!collection || collection.length === 0) {
    return null;
  }
  
  const randomIndex = Math.floor(Math.random() * collection.length);
  return collection[randomIndex];
};

/**
 * Get all ayat for a specific mood
 * @param {string} mood - The mood to get ayat for
 * @returns {array} Array of ayah objects
 */
export const getAyatByMood = (mood) => {
  return ayatCollections[mood] || [];
};

/**
 * Get ayat by category within a mood
 * @param {string} mood - The mood
 * @param {string} category - The category to filter by
 * @returns {array} Filtered array of ayah objects
 */
export const getAyatByCategory = (mood, category) => {
  const collection = ayatCollections[mood] || [];
  return collection.filter(ayah => ayah.category === category);
};

/**
 * Get all available categories for a mood
 * @param {string} mood - The mood
 * @returns {array} Array of unique categories
 */
export const getCategoriesForMood = (mood) => {
  const collection = ayatCollections[mood] || [];
  const categories = collection.map(ayah => ayah.category);
  return [...new Set(categories)];
};

/**
 * Search ayat by theme or content
 * @param {string} searchTerm - Term to search for
 * @param {string} mood - Optional mood to limit search to
 * @returns {array} Array of matching ayah objects
 */
export const searchAyat = (searchTerm, mood = null) => {
  const searchLower = searchTerm.toLowerCase();
  let collectionsToSearch = mood ? [ayatCollections[mood]] : Object.values(ayatCollections);
  
  const results = [];
  
  collectionsToSearch.forEach(collection => {
    if (collection) {
      const matches = collection.filter(ayah => 
        ayah.english.toLowerCase().includes(searchLower) ||
        ayah.theme.toLowerCase().includes(searchLower) ||
        ayah.category.toLowerCase().includes(searchLower)
      );
      results.push(...matches);
    }
  });
  
  return results;
};

/**
 * Get daily ayah (changes based on date)
 * @returns {object} Ayah object for today
 */
export const getDailyAyah = () => {
  // Get all ayat from all collections
  const allAyat = Object.values(ayatCollections).flat();
  
  // Use date as seed for consistent daily ayah
  const today = new Date();
  const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);
  const index = dayOfYear % allAyat.length;
  
  return allAyat[index];
};

/**
 * Get statistics about the ayat collection
 * @returns {object} Statistics object
 */
export const getAyatStats = () => {
  const stats = {
    total: 0,
    byMood: {},
    byCategory: {}
  };
  
  Object.entries(ayatCollections).forEach(([mood, collection]) => {
    stats.byMood[mood] = collection.length;
    stats.total += collection.length;
    
    collection.forEach(ayah => {
      if (!stats.byCategory[ayah.category]) {
        stats.byCategory[ayah.category] = 0;
      }
      stats.byCategory[ayah.category]++;
    });
  });
  
  return stats;
};

export default ayatCollections;
