import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Moon, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '../../utils/cn';

const IslamicCalendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [islamicDate, setIslamicDate] = useState(null);
  const [importantDates, setImportantDates] = useState([]);

  // Islamic months
  const islamicMonths = [
    '<PERSON><PERSON>ram', '<PERSON>far', '<PERSON><PERSON>\' al-awwal', '<PERSON><PERSON>\' al-thani',
    '<PERSON><PERSON> al-awwal', '<PERSON><PERSON> al-thani', '<PERSON><PERSON>', '<PERSON>ha\'ban',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> al-<PERSON>\'dah', 'Dhu al-Hijjah'
  ];

  // Important Islamic dates and events
  const islamicEvents = {
    1: { // Muharram
      1: 'Islamic New Year',
      10: 'Day of Ashura'
    },
    3: { // <PERSON><PERSON>' al-awwal
      12: '<PERSON><PERSON><PERSON> an-<PERSON><PERSON> (Prophet\'s Birthday)'
    },
    7: { // Rajab
      27: '<PERSON><PERSON> and <PERSON>\'raj (Night Journey)'
    },
    8: { // Sha'ban
      15: 'Laylat al-Bara\'at (Night of Forgiveness)'
    },
    9: { // Ramadan
      1: 'First Day of Ramadan',
      27: 'Laylat al-Qadr (Night of Power)'
    },
    10: { // Shawwal
      1: 'Eid al-Fitr'
    },
    12: { // Dhu al-Hijjah
      8: 'Day of Arafah',
      10: 'Eid al-Adha'
    }
  };

  // Convert Gregorian to Islamic date (simplified calculation)
  const gregorianToIslamic = (gregorianDate) => {
    // This is a simplified conversion. For production, use a proper Islamic calendar library
    const gregorianYear = gregorianDate.getFullYear();
    const gregorianMonth = gregorianDate.getMonth() + 1;
    const gregorianDay = gregorianDate.getDate();
    
    // Approximate conversion (not astronomically accurate)
    const islamicYear = Math.floor((gregorianYear - 622) * 1.030684);
    const islamicMonth = Math.floor(Math.random() * 12) + 1; // Simplified
    const islamicDay = Math.floor(Math.random() * 29) + 1; // Simplified
    
    return {
      year: islamicYear + 1,
      month: islamicMonth,
      day: islamicDay,
      monthName: islamicMonths[islamicMonth - 1]
    };
  };

  useEffect(() => {
    const islamic = gregorianToIslamic(currentDate);
    setIslamicDate(islamic);

    // Get important dates for current month
    const events = islamicEvents[islamic.month] || {};
    const monthEvents = Object.entries(events).map(([day, event]) => ({
      day: parseInt(day),
      event,
      isToday: parseInt(day) === islamic.day
    }));
    setImportantDates(monthEvents);
  }, [currentDate]);

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const getMoonPhase = () => {
    // Simplified moon phase calculation
    const phases = ['🌑', '🌒', '🌓', '🌔', '🌕', '🌖', '🌗', '🌘'];
    const dayOfMonth = islamicDate?.day || 1;
    const phaseIndex = Math.floor((dayOfMonth / 29) * 8) % 8;
    return phases[phaseIndex];
  };

  if (!islamicDate) {
    return (
      <div className="bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-gray-700/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
          <div className="h-32 bg-gray-300 dark:bg-gray-600 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-gray-700/50"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <motion.div
          className="flex items-center space-x-2"
          whileHover={{ scale: 1.05 }}
        >
          <Calendar className="w-6 h-6 text-emerald-500" />
          <h3 className="text-xl font-bold text-gray-800 dark:text-white">
            Islamic Calendar
          </h3>
        </motion.div>

        <div className="flex items-center space-x-2">
          <motion.button
            onClick={() => navigateMonth(-1)}
            className="p-2 rounded-lg bg-white/20 hover:bg-white/30 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="w-4 h-4 text-gray-700 dark:text-gray-300" />
          </motion.button>
          
          <motion.button
            onClick={() => navigateMonth(1)}
            className="p-2 rounded-lg bg-white/20 hover:bg-white/30 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="w-4 h-4 text-gray-700 dark:text-gray-300" />
          </motion.button>
        </div>
      </div>

      {/* Current Islamic Date */}
      <motion.div
        className="text-center mb-6 p-4 rounded-xl bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-200/30 dark:border-emerald-700/30"
        whileHover={{ scale: 1.02 }}
      >
        <div className="flex items-center justify-center space-x-3 mb-2">
          <span className="text-3xl">{getMoonPhase()}</span>
          <div>
            <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
              {islamicDate.day} {islamicDate.monthName}
            </div>
            <div className="text-lg text-gray-700 dark:text-gray-300">
              {islamicDate.year} AH
            </div>
          </div>
        </div>
        
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {currentDate.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </div>
      </motion.div>

      {/* Important Dates */}
      <div className="space-y-3">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
          <Star className="w-5 h-5 text-yellow-500 mr-2" />
          Important Dates This Month
        </h4>
        
        <AnimatePresence>
          {importantDates.length > 0 ? (
            <div className="space-y-2">
              {importantDates.map((event, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-all",
                    event.isToday
                      ? "bg-emerald-500/20 border border-emerald-500/30 shadow-lg"
                      : "bg-white/10 dark:bg-gray-700/30 hover:bg-white/20 dark:hover:bg-gray-600/30"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                      event.isToday
                        ? "bg-emerald-500 text-white"
                        : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"
                    )}>
                      {event.day}
                    </div>
                    <span className="text-gray-700 dark:text-gray-300 font-medium">
                      {event.event}
                    </span>
                  </div>
                  
                  {event.isToday && (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="text-emerald-500"
                    >
                      ✨
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-4 text-gray-500 dark:text-gray-400"
            >
              <Moon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No special events this month</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer Note */}
      <div className="mt-6 pt-4 border-t border-gray-200/30 dark:border-gray-700/30">
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          * Islamic dates are approximate and may vary by location
        </p>
      </div>
    </motion.div>
  );
};

export default IslamicCalendar;
