import React, { createContext, useContext, useState } from 'react';

const MoodContext = createContext();

export const useMood = () => {
  const context = useContext(MoodContext);
  if (!context) {
    throw new Error('useMood must be used within a MoodProvider');
  }
  return context;
};

export const MOODS = {
  NEUTRAL: 'neutral',
  SAD: 'sad',
  HAPPY: 'happy',
  ANGRY: 'angry',
  PEACEFUL: 'peaceful'
};

export const MOOD_CONFIGS = {
  [MOODS.NEUTRAL]: {
    emoji: '😌',
    name: 'Peaceful',
    color: 'from-blue-400 to-purple-500',
    bgColor: 'from-blue-50 to-purple-50',
    darkBgColor: 'from-blue-900/20 to-purple-900/20',
    description: 'Find inner peace and tranquility'
  },
  [MOODS.SAD]: {
    emoji: '😢',
    name: 'Seeking Hope',
    color: 'from-blue-500 to-indigo-600',
    bgColor: 'from-blue-50 to-indigo-50',
    darkBgColor: 'from-blue-900/20 to-indigo-900/20',
    description: 'Find comfort and hope in divine guidance'
  },
  [MOODS.HAPPY]: {
    emoji: '😄',
    name: 'Grateful',
    color: 'from-emerald-500 to-teal-600',
    bgColor: 'from-emerald-50 to-teal-50',
    darkBgColor: 'from-emerald-900/20 to-teal-900/20',
    description: 'Express gratitude and joy'
  },
  [MOODS.ANGRY]: {
    emoji: '😠',
    name: 'Seeking Calm',
    color: 'from-orange-500 to-red-500',
    bgColor: 'from-orange-50 to-red-50',
    darkBgColor: 'from-orange-900/20 to-red-900/20',
    description: 'Find peace and control your emotions'
  },
  [MOODS.PEACEFUL]: {
    emoji: '🕊️',
    name: 'Serene',
    color: 'from-green-400 to-blue-500',
    bgColor: 'from-green-50 to-blue-50',
    darkBgColor: 'from-green-900/20 to-blue-900/20',
    description: 'Embrace serenity and spiritual connection'
  }
};

export const MoodProvider = ({ children }) => {
  const [currentMood, setCurrentMood] = useState(MOODS.NEUTRAL);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const changeMood = async (newMood) => {
    if (newMood === currentMood) return;
    
    setIsTransitioning(true);
    
    // Add a small delay for smooth transition
    setTimeout(() => {
      setCurrentMood(newMood);
      setIsTransitioning(false);
    }, 300);
  };

  const getCurrentMoodConfig = () => MOOD_CONFIGS[currentMood];

  const value = {
    currentMood,
    changeMood,
    isTransitioning,
    getCurrentMoodConfig,
    MOODS,
    MOOD_CONFIGS
  };

  return (
    <MoodContext.Provider value={value}>
      {children}
    </MoodContext.Provider>
  );
};
