please listen to what i wont , remove correctly the current emojis and build again new 5D emojis model , this emojis should be visible to the eye of users with the emotions of this emojis , when i click to Full Quran Reader button i don't wont Quranic verses with the audio option , i wont , also the background of the quarn book is transparent , i wont the full quran books like the real book i mean every complete surah with all its verses, and all the surahs are arranged like the Qur’an. exactly like the realy quran book search for this to know what i wont , also make the background of quran book like the real one to make users feel like it's quran  book, take your time and build proffesional workplease listen to what i wont , remove correctly the current emojis and build again new 5D emojis model , this emojis should be visible to the eye of users with the emotions of this emojis , when i click to Full Quran Reader button i don't wont Quranic verses with the audio option , i wont , also the background of the quarn book is transparent , i wont the full quran books like the real book i mean every complete surah with all its verses, and all the surahs are arranged like the Qur’an. exactly like the realy quran book search for this to know what i wont , also make the background of quran book like the real one to make users feel like it's quran  book, take your time and build proffesional work/**
 * Islamic utility functions for prayer times, dates, and formatting
 */

/**
 * Get Islamic date (Hijri calendar)
 * Note: This is a simplified version. For production, use a proper Islamic calendar library
 */
export const getIslamicDate = () => {
  const gregorianDate = new Date();
  // This is a simplified calculation - in production, use a proper Hijri calendar library
  const hijriYear = gregorianDate.getFullYear() - 579;
  const hijriMonths = [
    'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani',
    'Jumada al-awwal', 'Jumada al-thani', 'Rajab', 'Sha\'ban',
    'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
  ];
  
  return {
    year: hijriYear,
    month: hijriMonths[gregorianDate.getMonth()],
    day: gregorianDate.getDate()
  };
};

/**
 * Format time for prayer times display
 */
export const formatPrayerTime = (time) => {
  if (!time) return '--:--';
  
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  
  return `${displayHour}:${minutes} ${ampm}`;
};

/**
 * Get prayer names in order
 */
export const getPrayerNames = () => [
  'Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'
];

/**
 * Get current prayer based on time
 */
export const getCurrentPrayer = (prayerTimes) => {
  if (!prayerTimes) return null;
  
  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();
  
  const prayers = getPrayerNames();
  let currentPrayer = prayers[prayers.length - 1]; // Default to Isha
  
  for (let i = 0; i < prayers.length; i++) {
    const prayer = prayers[i];
    const prayerTime = prayerTimes[prayer.toLowerCase()];
    
    if (prayerTime) {
      const [hours, minutes] = prayerTime.split(':');
      const prayerMinutes = parseInt(hours) * 60 + parseInt(minutes);
      
      if (currentTime < prayerMinutes) {
        currentPrayer = prayer;
        break;
      }
    }
  }
  
  return currentPrayer;
};

/**
 * Get next prayer time
 */
export const getNextPrayer = (prayerTimes) => {
  const currentPrayer = getCurrentPrayer(prayerTimes);
  const prayers = getPrayerNames();
  const currentIndex = prayers.indexOf(currentPrayer);
  
  if (currentIndex === -1 || currentIndex === prayers.length - 1) {
    return { name: prayers[0], time: prayerTimes?.fajr };
  }
  
  const nextPrayer = prayers[currentIndex + 1];
  return { 
    name: nextPrayer, 
    time: prayerTimes?.[nextPrayer.toLowerCase()] 
  };
};

/**
 * Generate Islamic greeting based on time of day
 */
export const getIslamicGreeting = () => {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return 'صباح الخير'; // Good morning
  } else if (hour < 18) {
    return 'مساء الخير'; // Good afternoon
  } else {
    return 'مساء النور'; // Good evening
  }
};

/**
 * Get Qibla direction (simplified - for production use proper geolocation)
 */
export const getQiblaDirection = (latitude, longitude) => {
  // Kaaba coordinates
  const kaabaLat = 21.4225;
  const kaabaLng = 39.8262;
  
  const dLng = (kaabaLng - longitude) * Math.PI / 180;
  const lat1 = latitude * Math.PI / 180;
  const lat2 = kaabaLat * Math.PI / 180;
  
  const y = Math.sin(dLng) * Math.cos(lat2);
  const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
  
  let bearing = Math.atan2(y, x) * 180 / Math.PI;
  bearing = (bearing + 360) % 360;
  
  return Math.round(bearing);
};
