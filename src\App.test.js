import { render, screen } from "@testing-library/react";
import App from "./App";

// Mock framer-motion
jest.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    section: ({ children, ...props }) => (
      <section {...props}>{children}</section>
    ),
    footer: ({ children, ...props }) => <footer {...props}>{children}</footer>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock @react-three/fiber and @react-three/drei
jest.mock("@react-three/fiber", () => ({
  Canvas: ({ children }) => <div data-testid="canvas">{children}</div>,
  useFrame: () => {},
}));

jest.mock("@react-three/drei", () => ({
  Text: ({ children }) => <div>{children}</div>,
  OrbitControls: () => null,
  Float: ({ children }) => <div>{children}</div>,
}));

test("renders Islamic website title", () => {
  render(<App />);
  const titleElement = screen.getByText(/Islamic Sanctuary/i);
  expect(titleElement).toBeInTheDocument();
});

test("renders mood selector", () => {
  render(<App />);
  const moodSelectorElement = screen.getByText(/How are you feeling today?/i);
  expect(moodSelectorElement).toBeInTheDocument();
});

test("renders footer message", () => {
  render(<App />);
  const footerElement = screen.getByText(
    /May Allah guide us all to the straight path/i
  );
  expect(footerElement).toBeInTheDocument();
});
