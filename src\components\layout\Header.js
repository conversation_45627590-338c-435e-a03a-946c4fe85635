import React, { useState } from "react";
import { Moon, Sun, Settings, <PERSON>lette, Star } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "../../contexts/ThemeContext";
import { cn } from "../../utils/cn";

const Header = () => {
  const { isDark, toggleTheme } = useTheme();
  const [isThemeChanging, setIsThemeChanging] = useState(false);

  const handleThemeToggle = () => {
    setIsThemeChanging(true);
    toggleTheme();
    setTimeout(() => setIsThemeChanging(false), 800);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-white/10">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full islamic-gradient flex items-center justify-center">
              <span className="text-white font-bold text-lg">☪</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient">
                Islamic Sanctuary
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Find peace in divine guidance
              </p>
            </div>
          </div>

          {/* Navigation and Controls */}
          <div className="flex items-center space-x-4">
            {/* Islamic Date */}
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                {new Date().toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                ١٤٤٦ هـ
              </p>
            </div>

            {/* Enhanced Theme Toggle */}
            <motion.button
              onClick={handleThemeToggle}
              className={cn(
                "relative p-3 rounded-full transition-all duration-500 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50 overflow-hidden",
                isThemeChanging && "animate-pulse"
              )}
              aria-label="Toggle theme"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Background animation */}
              <motion.div
                className={cn(
                  "absolute inset-0 rounded-full",
                  isDark
                    ? "bg-gradient-to-r from-blue-600 to-purple-600"
                    : "bg-gradient-to-r from-yellow-400 to-orange-500"
                )}
                initial={{ scale: 0, opacity: 0 }}
                animate={{
                  scale: isThemeChanging ? 1.5 : 0,
                  opacity: isThemeChanging ? 0.3 : 0,
                }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />

              {/* Icon with rotation animation */}
              <AnimatePresence mode="wait">
                {isDark ? (
                  <motion.div
                    key="sun"
                    initial={{ rotate: -180, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 180, opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Sun className="w-5 h-5 text-yellow-500 relative z-10" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="moon"
                    initial={{ rotate: 180, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -180, opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Moon className="w-5 h-5 text-blue-600 relative z-10" />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Sparkle effects */}
              {isThemeChanging && (
                <div className="absolute inset-0">
                  {[...Array(6)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full"
                      style={{
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`,
                      }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{
                        scale: [0, 1, 0],
                        opacity: [0, 1, 0],
                        rotate: [0, 360],
                      }}
                      transition={{
                        duration: 0.8,
                        delay: i * 0.1,
                        ease: "easeOut",
                      }}
                    />
                  ))}
                </div>
              )}
            </motion.button>

            {/* Modern Settings */}
            <motion.button
              className={cn(
                "relative p-3 rounded-full transition-all duration-300 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50 group"
              )}
              aria-label="Settings"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{ rotate: [0, 180, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="group-hover:animate-none"
              >
                <Palette className="w-5 h-5 text-gray-700 dark:text-gray-300" />
              </motion.div>

              {/* Hover effect */}
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"
                initial={{ scale: 0, opacity: 0 }}
                whileHover={{ scale: 1, opacity: 0.2 }}
                transition={{ duration: 0.3 }}
              />
            </motion.button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
