import React, { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Star, <PERSON>rkles } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme, THEMES } from "../../contexts/ThemeContext";
import { cn } from "../../utils/cn";

const Header = () => {
  const { currentTheme, changeTheme, getCurrentThemeConfig, isTransitioning } =
    useTheme();
  const [showThemeMenu, setShowThemeMenu] = useState(false);

  const handleBackgroundToggle = () => {
    // Cycle between light and dark (old functionality)
    const nextTheme =
      currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
    changeTheme(nextTheme);
  };

  const handleBlackModeToggle = () => {
    // Toggle black mode
    const nextTheme =
      currentTheme === THEMES.BLACK ? THEMES.LIGHT : THEMES.BLACK;
    changeTheme(nextTheme);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-white/10">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full islamic-gradient flex items-center justify-center">
              <span className="text-white font-bold text-lg">☪</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient">
                Islamic Sanctuary
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Find peace in divine guidance
              </p>
            </div>
          </div>

          {/* Navigation and Controls */}
          <div className="flex items-center space-x-4">
            {/* Islamic Date */}
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                {new Date().toLocaleDateString("en-US", {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                ١٤٤٦ هـ
              </p>
            </div>

            {/* Background Theme Toggle */}
            <motion.button
              onClick={handleBackgroundToggle}
              className={cn(
                "relative p-3 rounded-full transition-all duration-500 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50 overflow-hidden",
                isTransitioning && "animate-pulse"
              )}
              aria-label="Toggle background"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Background animation */}
              <motion.div
                className={cn(
                  "absolute inset-0 rounded-full",
                  currentTheme === THEMES.DARK
                    ? "bg-gradient-to-r from-blue-600 to-purple-600"
                    : "bg-gradient-to-r from-yellow-400 to-orange-500"
                )}
                initial={{ scale: 0, opacity: 0 }}
                animate={{
                  scale: isTransitioning ? 1.5 : 0,
                  opacity: isTransitioning ? 0.3 : 0,
                }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />

              {/* Icon with rotation animation */}
              <AnimatePresence mode="wait">
                {currentTheme === THEMES.DARK ? (
                  <motion.div
                    key="sun"
                    initial={{ rotate: -180, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 180, opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Sun className="w-5 h-5 text-yellow-500 relative z-10" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="palette"
                    initial={{ rotate: 180, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -180, opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Palette className="w-5 h-5 text-blue-600 relative z-10" />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Sparkle effects */}
              {isTransitioning && (
                <div className="absolute inset-0">
                  {[...Array(6)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-white rounded-full"
                      style={{
                        left: `${20 + Math.random() * 60}%`,
                        top: `${20 + Math.random() * 60}%`,
                      }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{
                        scale: [0, 1, 0],
                        opacity: [0, 1, 0],
                        rotate: [0, 360],
                      }}
                      transition={{
                        duration: 0.8,
                        delay: i * 0.1,
                        ease: "easeOut",
                      }}
                    />
                  ))}
                </div>
              )}
            </motion.button>

            {/* Black Mode Toggle */}
            <motion.button
              onClick={handleBlackModeToggle}
              className={cn(
                "relative p-3 rounded-full transition-all duration-500 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50 overflow-hidden",
                currentTheme === THEMES.BLACK && "ring-2 ring-purple-500",
                isTransitioning && "animate-pulse"
              )}
              aria-label="Toggle black mode"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              {/* Background animation for black mode */}
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-600 to-black"
                initial={{ scale: 0, opacity: 0 }}
                animate={{
                  scale:
                    isTransitioning && currentTheme === THEMES.BLACK ? 1.5 : 0,
                  opacity:
                    isTransitioning && currentTheme === THEMES.BLACK ? 0.3 : 0,
                }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />

              {/* Moon icon with special animation */}
              <motion.div
                animate={{
                  rotate: currentTheme === THEMES.BLACK ? [0, 360] : 0,
                  scale: currentTheme === THEMES.BLACK ? [1, 1.2, 1] : 1,
                }}
                transition={{
                  duration: currentTheme === THEMES.BLACK ? 2 : 0.3,
                  repeat: currentTheme === THEMES.BLACK ? Infinity : 0,
                  ease: "easeInOut",
                }}
              >
                <Moon
                  className={cn(
                    "w-5 h-5 relative z-10",
                    currentTheme === THEMES.BLACK
                      ? "text-purple-400"
                      : "text-gray-600 dark:text-gray-400"
                  )}
                />
              </motion.div>

              {/* Stars effect for black mode */}
              {currentTheme === THEMES.BLACK && (
                <div className="absolute inset-0">
                  {[...Array(4)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute"
                      style={{
                        left: `${15 + i * 20}%`,
                        top: `${15 + (i % 2) * 50}%`,
                      }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0, 1, 0],
                      }}
                      transition={{
                        duration: 2,
                        delay: i * 0.3,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    >
                      <Star className="w-2 h-2 text-purple-300" />
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
