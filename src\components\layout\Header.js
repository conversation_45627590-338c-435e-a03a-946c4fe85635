import React from 'react';
import { <PERSON>, <PERSON>, Settings } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { cn } from '../../utils/cn';

const Header = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-white/10">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full islamic-gradient flex items-center justify-center">
              <span className="text-white font-bold text-lg">☪</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gradient">
                Islamic Sanctuary
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Find peace in divine guidance
              </p>
            </div>
          </div>

          {/* Navigation and Controls */}
          <div className="flex items-center space-x-4">
            {/* Islamic Date */}
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-arabic">
                ١٤٤٦ هـ
              </p>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className={cn(
                "p-2 rounded-full transition-all duration-300 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50"
              )}
              aria-label="Toggle theme"
            >
              {isDark ? (
                <Sun className="w-5 h-5 text-yellow-500" />
              ) : (
                <Moon className="w-5 h-5 text-blue-600" />
              )}
            </button>

            {/* Settings */}
            <button
              className={cn(
                "p-2 rounded-full transition-all duration-300 hover:scale-110",
                "bg-white/20 hover:bg-white/30 dark:bg-gray-800/50 dark:hover:bg-gray-700/50",
                "border border-white/20 dark:border-gray-600/50"
              )}
              aria-label="Settings"
            >
              <Settings className="w-5 h-5 text-gray-700 dark:text-gray-300" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
