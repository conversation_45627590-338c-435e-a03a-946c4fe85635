import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { RotateCcw, Plus, Minus, Heart, Star } from "lucide-react";
import { cn } from "../../utils/cn";

const DhikrCounter = () => {
  const [count, setCount] = useState(0);
  const [selectedDhikr, setSelectedDhikr] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const dhikrList = [
    {
      id: 1,
      arabic: "سُبْحَانَ اللَّهِ",
      transliteration: "<PERSON>han<PERSON>llah",
      english: "Glory be to Allah",
      target: 33,
      color: "from-emerald-500 to-teal-600",
    },
    {
      id: 2,
      arabic: "الْحَمْدُ لِلَّهِ",
      transliteration: "<PERSON>ham<PERSON><PERSON><PERSON>",
      english: "All praise is due to <PERSON>",
      target: 33,
      color: "from-blue-500 to-indigo-600",
    },
    {
      id: 3,
      arabic: "اللَّهُ أَكْبَرُ",
      transliteration: "<PERSON><PERSON> <PERSON>",
      english: "<PERSON> is the Greatest",
      target: 34,
      color: "from-purple-500 to-pink-600",
    },
    {
      id: 4,
      arabic: "لَا إِلَٰهَ إِلَّا اللَّهُ",
      transliteration: "La ilaha illa <PERSON>",
      english: "There is no god but Allah",
      target: 100,
      color: "from-orange-500 to-red-500",
    },
    {
      id: 5,
      arabic: "أَسْتَغْفِرُ اللَّهَ",
      transliteration: "Astaghfirullah",
      english: "I seek forgiveness from Allah",
      target: 100,
      color: "from-green-500 to-emerald-600",
    },
  ];

  const currentDhikr = dhikrList[selectedDhikr];
  const progress = Math.min((count / currentDhikr.target) * 100, 100);
  const isComplete = count >= currentDhikr.target;

  useEffect(() => {
    // Load saved count from localStorage
    const savedCount = localStorage.getItem(`dhikr-${currentDhikr.id}`);
    if (savedCount) {
      setCount(parseInt(savedCount));
    } else {
      setCount(0);
    }
  }, [selectedDhikr, currentDhikr.id]);

  useEffect(() => {
    // Save count to localStorage
    localStorage.setItem(`dhikr-${currentDhikr.id}`, count.toString());
  }, [count, currentDhikr.id]);

  const handleIncrement = () => {
    setIsAnimating(true);
    setCount((prev) => prev + 1);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const handleDecrement = () => {
    if (count > 0) {
      setCount((prev) => prev - 1);
    }
  };

  const handleReset = () => {
    setCount(0);
  };

  const handleDhikrChange = (index) => {
    setSelectedDhikr(index);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass-effect rounded-2xl p-6 border border-white/20 shadow-xl"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div
            className={cn(
              "w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-r",
              currentDhikr.color
            )}
          >
            <Heart className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
              Dhikr Counter
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Remember Allah with every breath
            </p>
          </div>
        </div>

        {isComplete && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="flex items-center space-x-1 text-emerald-500"
          >
            <Star className="w-5 h-5 fill-current" />
            <span className="text-sm font-medium">Complete!</span>
          </motion.div>
        )}
      </div>

      {/* Modern Dhikr Selection Buttons */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
          Choose your dhikr:
        </h4>
        <div className="flex flex-wrap gap-3 justify-center">
          {dhikrList.map((dhikr, index) => (
            <motion.button
              key={dhikr.id}
              onClick={() => handleDhikrChange(index)}
              className={cn(
                "relative px-4 py-2 rounded-xl text-center transition-all duration-300 border-2 min-w-[140px] btn btn-3d has-shine",
                selectedDhikr === index
                  ? `border-transparent bg-gradient-to-r ${dhikr.color} text-white shadow-lg`
                  : "border-gray-200 dark:border-gray-700 bg-white/70 dark:bg-gray-800/70 hover:bg-white/90 dark:hover:bg-gray-800/90 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-md"
              )}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              layout
            >
              {/* Content */}
              <div className="relative z-10">
                {/* Arabic Text */}
                <div className="text-sm font-arabic mb-1">{dhikr.arabic}</div>

                {/* Transliteration */}
                <div
                  className={cn(
                    "font-medium text-xs",
                    selectedDhikr === index
                      ? "text-white"
                      : "text-gray-700 dark:text-gray-300"
                  )}
                >
                  {dhikr.transliteration}
                </div>

                {/* Target Count Badge */}
                <div
                  className={cn(
                    "text-xs mt-1 opacity-75",
                    selectedDhikr === index
                      ? "text-white"
                      : "text-emerald-600 dark:text-emerald-400"
                  )}
                >
                  {dhikr.target}x
                </div>

                {/* Selection Indicator */}
                {selectedDhikr === index && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="absolute -top-1 -right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center shadow-lg"
                  >
                    <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                  </motion.div>
                )}
              </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Current Dhikr Display */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedDhikr}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="text-center mb-6"
        >
          <div className="mb-4">
            <p className="text-3xl font-arabic text-gray-800 dark:text-gray-200 mb-2">
              {currentDhikr.arabic}
            </p>
            <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">
              {currentDhikr.transliteration}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {currentDhikr.english}
            </p>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Counter Display */}
      <div className="text-center mb-6">
        <motion.div
          className={cn(
            "inline-flex items-center justify-center w-32 h-32 rounded-full border-8 mb-4",
            isComplete
              ? "border-emerald-500 bg-emerald-500/10"
              : "border-gray-300 dark:border-gray-600"
          )}
          animate={isAnimating ? { scale: [1, 1.1, 1] } : {}}
          transition={{ duration: 0.3 }}
        >
          <span
            className={cn(
              "text-4xl font-bold",
              isComplete
                ? "text-emerald-600 dark:text-emerald-400"
                : "text-gray-800 dark:text-gray-200"
            )}
          >
            {count}
          </span>
        </motion.div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
          <motion.div
            className={cn(
              "h-2 rounded-full bg-gradient-to-r",
              currentDhikr.color
            )}
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        <p className="text-sm text-gray-600 dark:text-gray-400">
          {count} / {currentDhikr.target} ({Math.round(progress)}%)
        </p>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center space-x-4">
        <motion.button
          onClick={handleDecrement}
          disabled={count === 0}
          className={cn(
            "p-3 rounded-full transition-all",
            count === 0
              ? "bg-gray-100 dark:bg-gray-800 text-gray-400 cursor-not-allowed"
              : "bg-red-100 dark:bg-red-900/30 text-red-600 hover:bg-red-200 dark:hover:bg-red-900/50"
          )}
          whileHover={count > 0 ? { scale: 1.1 } : {}}
          whileTap={count > 0 ? { scale: 0.9 } : {}}
        >
          <Minus className="w-5 h-5" />
        </motion.button>

        <motion.button
          onClick={handleIncrement}
          className={cn(
            "p-4 rounded-full bg-gradient-to-r text-white shadow-lg",
            currentDhikr.color
          )}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Plus className="w-6 h-6" />
        </motion.button>

        <motion.button
          onClick={handleReset}
          className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <RotateCcw className="w-5 h-5" />
        </motion.button>
      </div>

      {/* Completion Message */}
      <AnimatePresence>
        {isComplete && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-4 p-3 rounded-lg bg-emerald-500/20 border border-emerald-500/30 text-center"
          >
            <p className="text-emerald-600 dark:text-emerald-400 font-medium">
              Barakallahu feeki! You've completed your dhikr target! 🌟
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default DhikrCounter;
