import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { RefreshCw, Heart, Share2, BookOpen } from "lucide-react";
import { useMood } from "../../contexts/MoodContext";
import { getRandomAyah } from "../../data/ayat";
import { cn } from "../../utils/cn";
import AudioPlayer from "../ui/AudioPlayer";

const AyahDisplay = () => {
  const { currentMood, getCurrentMoodConfig, isTransitioning } = useMood();
  const [currentAyah, setCurrentAyah] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const moodConfig = getCurrentMoodConfig();

  // Load ayah when mood changes
  useEffect(() => {
    if (!isTransitioning) {
      loadNewAyah();
    }
  }, [currentMood, isTransitioning]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadNewAyah = async () => {
    setIsLoading(true);

    // Add a small delay for better UX
    setTimeout(() => {
      const ayah = getRandomAyah(currentMood);
      setCurrentAyah(ayah);
      setIsLoading(false);
      setIsFavorited(false);
    }, 500);
  };

  const parseReference = (reference) => {
    // Expect formats like "Quran 13:28" or "Quran 89:27-28"
    if (!reference) return { surah: null, ayah: null };
    const match = reference.match(/(\d+)\s*:\s*([\d-]+)/);
    if (!match) return { surah: null, ayah: null };
    const surah = parseInt(match[1], 10);
    const ayahPart = match[2];
    const ayah = parseInt(ayahPart.split("-")[0], 10);
    return { surah, ayah };
  };

  const handleRefresh = () => {
    if (!isLoading) {
      loadNewAyah();
    }
  };

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
    // Here you could save to localStorage or send to backend
  };

  const handleShare = async () => {
    if (currentAyah && navigator.share) {
      try {
        await navigator.share({
          title: "Islamic Guidance",
          text: `${currentAyah.english}\n\n- ${currentAyah.reference}`,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(
          `${currentAyah.english}\n\n- ${currentAyah.reference}`
        );
      }
    }
  };

  if (!currentAyah && !isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400">
          Select a mood to receive divine guidance
        </p>
      </div>
    );
  }

  return (
    <div id="guidance" className="w-full max-w-4xl mx-auto">
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-12"
          >
            <div className="inline-flex items-center space-x-3">
              <RefreshCw className="w-6 h-6 animate-spin text-emerald-500" />
              <span className="text-lg text-gray-600 dark:text-gray-400">
                Finding guidance for you...
              </span>
            </div>
          </motion.div>
        ) : currentAyah ? (
          <motion.div
            key={currentAyah.id}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className={cn(
              "glass-effect rounded-3xl p-8 border border-white/20",
              "shadow-2xl backdrop-blur-xl"
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div
                  className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center",
                    `bg-gradient-to-r ${moodConfig.color}`
                  )}
                >
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                    {moodConfig.name} Guidance
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {currentAyah.theme}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={handleFavorite}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={cn(
                    "p-2 rounded-full transition-colors",
                    isFavorited
                      ? "bg-red-100 text-red-500 dark:bg-red-900/30"
                      : "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400"
                  )}
                >
                  <Heart
                    className={cn("w-5 h-5", isFavorited && "fill-current")}
                  />
                </motion.button>

                <motion.button
                  onClick={handleShare}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 rounded-full bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 transition-colors"
                >
                  <Share2 className="w-5 h-5" />
                </motion.button>

                <motion.button
                  onClick={handleRefresh}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 rounded-full bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 transition-colors"
                >
                  <RefreshCw className="w-5 h-5" />
                </motion.button>
              </div>
            </div>

            {/* Arabic Text */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-6"
            >
              <p className="text-2xl md:text-3xl font-arabic text-right leading-relaxed text-gray-900 dark:text-white mb-4">
                {currentAyah.arabic}
              </p>
            </motion.div>

            {/* English Translation */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-6"
            >
              <p className="text-lg md:text-xl leading-relaxed text-gray-700 dark:text-gray-100 italic">
                "{currentAyah.english}"
              </p>
            </motion.div>

            {/* Audio Player */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="mb-6"
            >
              <AudioPlayer
                surah={
                  (currentAyah && currentAyah.surah) ||
                  parseReference(currentAyah?.reference).surah
                }
                ayah={
                  (currentAyah && currentAyah.ayah) ||
                  parseReference(currentAyah?.reference).ayah
                }
                reciter="luhaidan"
                showVolumeControl={true}
              />
            </motion.div>

            {/* Reference and Category */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-700/50"
            >
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {currentAyah.reference}
                </span>
                <span
                  className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium",
                    "bg-gradient-to-r text-white",
                    moodConfig.color
                  )}
                >
                  {currentAyah.category}
                </span>
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-500">
                Tap refresh for another verse
              </div>
            </motion.div>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
};

export default AyahDisplay;
