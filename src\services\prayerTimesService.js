/**
 * Prayer Times Service for Tunisia (Ariana)
 * Uses the Aladhan API for accurate Islamic prayer times
 */

const TUNISIA_COORDINATES = {
  latitude: 36.8665,
  longitude: 10.1647,
  city: 'Ariana',
  country: 'Tunisia',
  timezone: 'Africa/Tunis'
};

/**
 * Fetch prayer times from Aladhan API
 * @param {Date} date - Date for which to fetch prayer times
 * @returns {Promise<Object>} Prayer times object
 */
export const fetchPrayerTimes = async (date = new Date()) => {
  try {
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // JavaScript months are 0-indexed
    const day = date.getDate();
    
    const url = `https://api.aladhan.com/v1/calendar/${year}/${month}?latitude=${TUNISIA_COORDINATES.latitude}&longitude=${TUNISIA_COORDINATES.longitude}&method=3`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.code !== 200 || !data.data || !data.data[day - 1]) {
      throw new Error('Invalid response from prayer times API');
    }
    
    const dayData = data.data[day - 1];
    const timings = dayData.timings;
    
    return {
      fajr: formatTime(timings.Fajr),
      dhuhr: formatTime(timings.Dhuhr),
      asr: formatTime(timings.Asr),
      maghrib: formatTime(timings.Maghrib),
      isha: formatTime(timings.Isha),
      sunrise: formatTime(timings.Sunrise),
      sunset: formatTime(timings.Sunset),
      date: dayData.date.readable,
      hijriDate: dayData.date.hijri.date,
      location: TUNISIA_COORDINATES
    };
  } catch (error) {
    console.error('Error fetching prayer times:', error);
    // Return fallback times for Tunisia
    return getFallbackPrayerTimes();
  }
};

/**
 * Format time string to remove timezone info
 * @param {string} timeString - Time string from API
 * @returns {string} Formatted time
 */
const formatTime = (timeString) => {
  if (!timeString) return '00:00';
  
  // Remove timezone info (e.g., "(CET)" or "(+01)")
  return timeString.split(' ')[0];
};

/**
 * Get fallback prayer times for Tunisia (approximate)
 * @returns {Object} Fallback prayer times
 */
const getFallbackPrayerTimes = () => {
  const now = new Date();
  const month = now.getMonth() + 1;
  
  // Approximate prayer times for Tunisia based on season
  let times;
  if (month >= 4 && month <= 9) {
    // Summer times
    times = {
      fajr: '04:30',
      dhuhr: '12:30',
      asr: '16:15',
      maghrib: '19:30',
      isha: '21:00'
    };
  } else {
    // Winter times
    times = {
      fajr: '05:45',
      dhuhr: '12:15',
      asr: '15:00',
      maghrib: '17:45',
      isha: '19:15'
    };
  }
  
  return {
    ...times,
    sunrise: '06:30',
    sunset: times.maghrib,
    date: now.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    hijriDate: getApproximateHijriDate(),
    location: TUNISIA_COORDINATES
  };
};

/**
 * Get approximate Hijri date
 * @returns {string} Approximate Hijri date
 */
const getApproximateHijriDate = () => {
  const gregorianDate = new Date();
  const hijriYear = gregorianDate.getFullYear() - 579;
  const hijriMonths = [
    'Muharram', 'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani',
    'Jumada al-awwal', 'Jumada al-thani', 'Rajab', 'Sha\'ban',
    'Ramadan', 'Shawwal', 'Dhu al-Qi\'dah', 'Dhu al-Hijjah'
  ];
  
  const monthIndex = gregorianDate.getMonth();
  const day = gregorianDate.getDate();
  
  return `${day} ${hijriMonths[monthIndex]} ${hijriYear}`;
};

/**
 * Get current prayer based on current time
 * @param {Object} prayerTimes - Prayer times object
 * @returns {string} Current prayer name
 */
export const getCurrentPrayer = (prayerTimes) => {
  if (!prayerTimes) return null;
  
  const now = new Date();
  const currentMinutes = now.getHours() * 60 + now.getMinutes();
  
  const prayers = [
    { name: 'Fajr', time: prayerTimes.fajr },
    { name: 'Dhuhr', time: prayerTimes.dhuhr },
    { name: 'Asr', time: prayerTimes.asr },
    { name: 'Maghrib', time: prayerTimes.maghrib },
    { name: 'Isha', time: prayerTimes.isha }
  ];
  
  let currentPrayer = 'Isha'; // Default to last prayer
  
  for (let i = 0; i < prayers.length; i++) {
    const prayer = prayers[i];
    const [hours, minutes] = prayer.time.split(':').map(Number);
    const prayerMinutes = hours * 60 + minutes;
    
    if (currentMinutes < prayerMinutes) {
      currentPrayer = prayer.name;
      break;
    }
  }
  
  return currentPrayer;
};

/**
 * Get next prayer time
 * @param {Object} prayerTimes - Prayer times object
 * @returns {Object} Next prayer info
 */
export const getNextPrayer = (prayerTimes) => {
  if (!prayerTimes) return null;
  
  const currentPrayer = getCurrentPrayer(prayerTimes);
  const prayers = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
  const currentIndex = prayers.indexOf(currentPrayer);
  
  if (currentIndex === -1 || currentIndex === prayers.length - 1) {
    return {
      name: 'Fajr',
      time: prayerTimes.fajr,
      isNextDay: true
    };
  }
  
  const nextPrayer = prayers[currentIndex + 1];
  return {
    name: nextPrayer,
    time: prayerTimes[nextPrayer.toLowerCase()],
    isNextDay: false
  };
};

/**
 * Calculate time remaining until next prayer
 * @param {Object} nextPrayer - Next prayer object
 * @returns {string} Time remaining string
 */
export const getTimeUntilNextPrayer = (nextPrayer) => {
  if (!nextPrayer || !nextPrayer.time) return '';
  
  const now = new Date();
  const [hours, minutes] = nextPrayer.time.split(':').map(Number);
  
  let nextPrayerTime = new Date();
  nextPrayerTime.setHours(hours, minutes, 0, 0);
  
  // If it's next day's Fajr
  if (nextPrayer.isNextDay) {
    nextPrayerTime.setDate(nextPrayerTime.getDate() + 1);
  }
  
  const timeDiff = nextPrayerTime - now;
  
  if (timeDiff <= 0) return '';
  
  const hoursLeft = Math.floor(timeDiff / (1000 * 60 * 60));
  const minutesLeft = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hoursLeft > 0) {
    return `${hoursLeft}h ${minutesLeft}m`;
  } else {
    return `${minutesLeft}m`;
  }
};

export default {
  fetchPrayerTimes,
  getCurrentPrayer,
  getNextPrayer,
  getTimeUntilNextPrayer,
  TUNISIA_COORDINATES
};
