import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Navigation,
  MapPin,
  Compass,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { cn } from "../../utils/cn";

const QiblaDirection = () => {
  const [qiblaDirection, setQiblaDirection] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [deviceOrientation, setDeviceOrientation] = useState(0);
  const [isCompassSupported, setIsCompassSupported] = useState(false);

  // Kaaba coordinates
  const KAABA_LAT = 21.4225;
  const KAABA_LNG = 39.8262;

  useEffect(() => {
    // Check if device orientation is supported
    if (window.DeviceOrientationEvent) {
      setIsCompassSupported(true);

      const handleOrientation = (event) => {
        if (event.alpha !== null) {
          setDeviceOrientation(event.alpha);
        }
      };

      window.addEventListener("deviceorientation", handleOrientation);
      return () =>
        window.removeEventListener("deviceorientation", handleOrientation);
    }
  }, []);

  const calculateQiblaDirection = (userLat, userLng) => {
    const toRadians = (degrees) => degrees * (Math.PI / 180);
    const toDegrees = (radians) => radians * (180 / Math.PI);

    const lat1 = toRadians(userLat);
    const lat2 = toRadians(KAABA_LAT);
    const deltaLng = toRadians(KAABA_LNG - userLng);

    const y = Math.sin(deltaLng) * Math.cos(lat2);
    const x =
      Math.cos(lat1) * Math.sin(lat2) -
      Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLng);

    let bearing = toDegrees(Math.atan2(y, x));
    return (bearing + 360) % 360; // Normalize to 0-360
  };

  const getCurrentLocation = () => {
    setIsLoading(true);
    setError(null);

    if (!navigator.geolocation) {
      setError("Geolocation is not supported by this browser");
      setIsLoading(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ lat: latitude, lng: longitude });

        const qibla = calculateQiblaDirection(latitude, longitude);
        setQiblaDirection(qibla);
        setIsLoading(false);
      },
      (error) => {
        setError(
          "Unable to get your location. Please enable location services."
        );
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  };

  const getCompassDirection = () => {
    if (qiblaDirection === null || !isCompassSupported) return 0;
    return qiblaDirection - deviceOrientation;
  };

  const getDirectionText = (direction) => {
    const directions = [
      "North",
      "North-East",
      "East",
      "South-East",
      "South",
      "South-West",
      "West",
      "North-West",
    ];
    const index = Math.round(direction / 45) % 8;
    return directions[index];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-gray-700/50"
    >
      <div className="text-center">
        <motion.div
          className="flex items-center justify-center mb-4"
          whileHover={{ scale: 1.05 }}
        >
          <Navigation className="w-6 h-6 text-emerald-500 mr-2" />
          <h3 className="text-xl font-bold text-gray-800 dark:text-white">
            Qibla Direction
          </h3>
        </motion.div>

        <AnimatePresence mode="wait">
          {!userLocation ? (
            <motion.div
              key="no-location"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="space-y-4"
            >
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Find the direction to Kaaba from your location
              </p>

              <motion.button
                onClick={getCurrentLocation}
                disabled={isLoading}
                className={cn(
                  "flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all",
                  "bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg hover:shadow-xl",
                  isLoading && "opacity-50 cursor-not-allowed"
                )}
                whileHover={!isLoading ? { scale: 1.05 } : {}}
                whileTap={!isLoading ? { scale: 0.95 } : {}}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Getting Location...</span>
                  </>
                ) : (
                  <>
                    <MapPin className="w-5 h-5" />
                    <span>Get My Location</span>
                  </>
                )}
              </motion.button>

              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center justify-center space-x-2 text-red-500 text-sm"
                >
                  <AlertCircle className="w-4 h-4" />
                  <span>{error}</span>
                </motion.div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="has-location"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="space-y-6"
            >
              {/* Compass */}
              <div className="relative w-48 h-48 mx-auto">
                {/* Compass Background */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-emerald-100 to-blue-100 dark:from-emerald-900/30 dark:to-blue-900/30 border-4 border-emerald-200 dark:border-emerald-700">
                  {/* Compass Markings */}
                  {[...Array(12)].map((_, i) => (
                    <div
                      key={i}
                      className="absolute w-1 h-6 bg-gray-400 dark:bg-gray-600"
                      style={{
                        left: "50%",
                        top: "8px",
                        transformOrigin: "50% 88px",
                        transform: `translateX(-50%) rotate(${i * 30}deg)`,
                      }}
                    />
                  ))}

                  {/* Cardinal Directions */}
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-sm font-bold text-gray-700 dark:text-gray-300">
                    N
                  </div>
                  <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-sm font-bold text-gray-700 dark:text-gray-300">
                    S
                  </div>
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-sm font-bold text-gray-700 dark:text-gray-300">
                    E
                  </div>
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-sm font-bold text-gray-700 dark:text-gray-300">
                    W
                  </div>
                </div>

                {/* Qibla Arrow */}
                <motion.div
                  className="absolute inset-0 flex items-center justify-center"
                  animate={{
                    rotate: isCompassSupported
                      ? getCompassDirection()
                      : qiblaDirection,
                  }}
                  transition={{ type: "spring", stiffness: 100, damping: 20 }}
                >
                  <motion.div
                    className="w-1 h-20 bg-gradient-to-t from-emerald-500 to-emerald-300 rounded-full shadow-lg"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    style={{
                      clipPath: "polygon(40% 0%, 60% 0%, 100% 100%, 0% 100%)",
                    }}
                  />
                </motion.div>

                {/* Center Dot */}
                <div className="absolute top-1/2 left-1/2 w-3 h-3 bg-emerald-500 rounded-full transform -translate-x-1/2 -translate-y-1/2 shadow-lg" />

                {/* Kaaba Icon */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                  animate={{
                    rotate: isCompassSupported
                      ? getCompassDirection()
                      : qiblaDirection,
                  }}
                  transition={{ type: "spring", stiffness: 100, damping: 20 }}
                >
                  <div className="w-6 h-6 bg-black rounded-sm shadow-lg transform translate-y-8" />
                </motion.div>
              </div>

              {/* Direction Info */}
              <div className="space-y-2">
                <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                  {Math.round(qiblaDirection)}°
                </div>
                <div className="text-lg text-gray-700 dark:text-gray-300">
                  {getDirectionText(qiblaDirection)}
                </div>
                {isCompassSupported && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    📱 Compass enabled - rotate your device
                  </div>
                )}
              </div>

              {/* Refresh Button */}
              <motion.button
                onClick={getCurrentLocation}
                className="btn btn-ghost btn-3d px-4 py-2 rounded-xl text-emerald-700 dark:text-emerald-400 border-emerald-300/40 dark:border-emerald-700/40"
                whileHover={{ scale: 1.05, y: -1 }}
                whileTap={{ scale: 0.98 }}
              >
                🔄 Refresh Location
              </motion.button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default QiblaDirection;
