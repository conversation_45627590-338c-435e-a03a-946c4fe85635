import React from 'react';
import { useMood } from '../../contexts/MoodContext';
import { cn } from '../../utils/cn';
import Header from './Header';
import BackgroundAnimation from '../ui/BackgroundAnimation';

const Layout = ({ children }) => {
  const { getCurrentMoodConfig, isTransitioning } = useMood();
  const moodConfig = getCurrentMoodConfig();

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <BackgroundAnimation />
      
      {/* Mood-based background overlay */}
      <div 
        className={cn(
          "fixed inset-0 transition-all duration-1000 opacity-30",
          `bg-gradient-to-br ${moodConfig.darkBgColor}`,
          isTransitioning && "opacity-0"
        )}
      />
      
      {/* Header */}
      <Header />
      
      {/* Main Content */}
      <main className="relative z-10 pt-20">
        <div className="container mx-auto px-4 py-8">
          {children}
        </div>
      </main>
      
      {/* Floating particles for ambiance */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "absolute w-1 h-1 bg-white/20 rounded-full",
              "animate-float"
            )}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 6}s`,
              animationDuration: `${6 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default Layout;
