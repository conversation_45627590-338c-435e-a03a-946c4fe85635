import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider } from '../../../contexts/ThemeContext';
import { MoodProvider } from '../../../contexts/MoodContext';
import MoodSelector from '../MoodSelector';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    section: ({ children, ...props }) => <section {...props}>{children}</section>,
    button: ({ children, ...props }) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock @react-three/fiber and @react-three/drei
jest.mock('@react-three/fiber', () => ({
  Canvas: ({ children }) => <div data-testid="canvas">{children}</div>,
  useFrame: () => {},
}));

jest.mock('@react-three/drei', () => ({
  Text: ({ children }) => <div>{children}</div>,
  OrbitControls: () => null,
  Float: ({ children }) => <div>{children}</div>,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider>
    <MoodProvider>
      {children}
    </MoodProvider>
  </ThemeProvider>
);

describe('MoodSelector', () => {
  test('renders mood selector title', () => {
    render(
      <TestWrapper>
        <MoodSelector />
      </TestWrapper>
    );
    
    expect(screen.getByText('How are you feeling today?')).toBeInTheDocument();
    expect(screen.getByText('Select your mood to receive guidance from the Quran')).toBeInTheDocument();
  });

  test('renders 3D canvas', () => {
    render(
      <TestWrapper>
        <MoodSelector />
      </TestWrapper>
    );
    
    expect(screen.getByTestId('canvas')).toBeInTheDocument();
  });

  test('renders mood labels', () => {
    render(
      <TestWrapper>
        <MoodSelector />
      </TestWrapper>
    );
    
    expect(screen.getByText('Seeking Hope')).toBeInTheDocument();
    expect(screen.getByText('Grateful')).toBeInTheDocument();
    expect(screen.getByText('Peaceful')).toBeInTheDocument();
    expect(screen.getByText('Seeking Calm')).toBeInTheDocument();
  });

  test('mood buttons are clickable', () => {
    render(
      <TestWrapper>
        <MoodSelector />
      </TestWrapper>
    );
    
    const gratefulButton = screen.getByText('Grateful');
    fireEvent.click(gratefulButton);
    
    // The button should still be in the document after clicking
    expect(gratefulButton).toBeInTheDocument();
  });
});
