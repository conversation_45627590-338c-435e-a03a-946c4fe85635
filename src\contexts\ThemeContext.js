import React, { createContext, useContext, useState, useEffect } from "react";

const ThemeContext = createContext();

export const THEMES = {
  LIGHT: "light",
  DARK: "dark",
  BLACK: "black",
};

export const THEME_CONFIGS = {
  [THEMES.LIGHT]: {
    name: "Light Mode",
    icon: "☀️",
    background: "bg-gradient-to-br from-blue-50 via-white to-emerald-50",
    description: "Bright and clean interface",
  },
  [THEMES.DARK]: {
    name: "Dark Mode",
    icon: "🌙",
    background: "bg-gradient-to-br from-gray-900 via-gray-800 to-emerald-900",
    description: "Easy on the eyes",
  },
  [THEMES.BLACK]: {
    name: "Black Mode",
    icon: "🌑",
    background: "bg-black",
    description: "Pure black with animated stars",
  },
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(() => {
    const saved = localStorage.getItem("islamic-web-theme");
    if (saved && Object.values(THEMES).includes(saved)) {
      return saved;
    }
    return window.matchMedia("(prefers-color-scheme: dark)").matches
      ? THEMES.DARK
      : THEMES.LIGHT;
  });

  const [isTransitioning, setIsTransitioning] = useState(false);

  const applyThemeToDocument = (theme) => {
    const root = document.documentElement;

    // Remove all theme classes
    root.classList.remove("light", "dark", "black");

    // Add new theme class
    root.classList.add(theme);

    // Apply special styles for black mode
    if (theme === THEMES.BLACK) {
      document.body.style.background = "#000000";
      document.body.style.color = "#ffffff";
    } else {
      document.body.style.background = "";
      document.body.style.color = "";
    }
  };

  useEffect(() => {
    applyThemeToDocument(currentTheme);
    localStorage.setItem("islamic-web-theme", currentTheme);
  }, [currentTheme]);

  const changeTheme = async (newTheme) => {
    if (newTheme === currentTheme || isTransitioning) return;

    setIsTransitioning(true);

    try {
      setCurrentTheme(newTheme);
      await new Promise((resolve) => setTimeout(resolve, 300));
    } catch (error) {
      console.error("Error changing theme:", error);
    } finally {
      setIsTransitioning(false);
    }
  };

  const getNextTheme = () => {
    const themes = Object.values(THEMES);
    const currentIndex = themes.indexOf(currentTheme);
    return themes[(currentIndex + 1) % themes.length];
  };

  const getCurrentThemeConfig = () => {
    return THEME_CONFIGS[currentTheme];
  };

  // Legacy support
  const isDark = currentTheme === THEMES.DARK;
  const toggleTheme = () => {
    const nextTheme =
      currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
    changeTheme(nextTheme);
  };

  const value = {
    currentTheme,
    changeTheme,
    getNextTheme,
    getCurrentThemeConfig,
    isTransitioning,
    THEMES,
    THEME_CONFIGS,
    // Legacy support
    isDark,
    toggleTheme,
    theme: currentTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};
