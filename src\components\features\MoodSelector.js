import React, { useState, useRef } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { Text, OrbitControls, Float } from "@react-three/drei";
import { motion, AnimatePresence } from "framer-motion";
import { useMood, MOODS, MOOD_CONFIGS } from "../../contexts/MoodContext";
import { cn } from "../../utils/cn";

// Enhanced 3D Emoji Component with Dynamic Reactions using HTML
const EmojiMesh = ({
  emoji,
  position,
  isSelected,
  onClick,
  mood,
  moodConfig,
}) => {
  const meshRef = useRef();
  const groupRef = useRef();
  const [hovered, setHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Dynamic emoji expressions based on mood and interaction
  const getEmojiExpression = () => {
    if (isAnimating) {
      switch (mood) {
        case "sad":
          return "😭"; // More expressive when clicked
        case "happy":
          return "😆"; // More joyful when clicked
        case "angry":
          return "😡"; // More intense when clicked
        case "peaceful":
          return "😇"; // More serene when clicked
        default:
          return emoji;
      }
    }

    if (hovered) {
      switch (mood) {
        case "sad":
          return "🥺"; // Slightly more sad when hovered
        case "happy":
          return "😊"; // Gentle smile when hovered
        case "angry":
          return "😤"; // Frustrated when hovered
        case "peaceful":
          return "😌"; // Calm when hovered
        default:
          return emoji;
      }
    }

    return emoji;
  };

  useFrame((state) => {
    if (groupRef.current) {
      // Gentle floating animation (no rotation)
      const floatOffset = Math.sin(state.clock.elapsedTime * 1.5) * 0.1;
      groupRef.current.position.y = position[1] + floatOffset;

      // 3D movement and reactions
      if (isAnimating) {
        // Excited bouncing when clicked
        const bounce = Math.sin(state.clock.elapsedTime * 10) * 0.2;
        groupRef.current.position.y = position[1] + floatOffset + bounce;

        // Slight rotation for dynamic effect
        groupRef.current.rotation.z =
          Math.sin(state.clock.elapsedTime * 8) * 0.1;

        // Scale pulsing
        const pulse = 1.3 + Math.sin(state.clock.elapsedTime * 12) * 0.1;
        groupRef.current.scale.setScalar(pulse);
      } else if (isSelected) {
        // Gentle breathing effect for selected emoji
        const breathe = 1.2 + Math.sin(state.clock.elapsedTime * 3) * 0.05;
        groupRef.current.scale.setScalar(breathe);

        // Gentle swaying
        groupRef.current.rotation.z =
          Math.sin(state.clock.elapsedTime * 2) * 0.05;
      } else if (hovered) {
        // Hover effect
        const pulse = 1.1 + Math.sin(state.clock.elapsedTime * 4) * 0.03;
        groupRef.current.scale.setScalar(pulse);

        // Slight tilt on hover
        groupRef.current.rotation.z =
          Math.sin(state.clock.elapsedTime * 3) * 0.03;
      } else {
        // Return to normal
        groupRef.current.scale.lerp({ x: 1, y: 1, z: 1 }, 0.1);
        groupRef.current.rotation.z *= 0.95; // Smooth rotation return
      }
    }
  });

  const handleClick = () => {
    setIsAnimating(true);
    onClick();
    // Reset animation after 2 seconds
    setTimeout(() => setIsAnimating(false), 2000);
  };

  return (
    <group
      ref={groupRef}
      position={position}
      onClick={handleClick}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      {/* Glowing background sphere */}
      <mesh ref={meshRef}>
        <sphereGeometry args={[0.9, 32, 32]} />
        <meshStandardMaterial
          color={
            isSelected
              ? moodConfig.color.split(" ")[1]
              : hovered
              ? "#34d399"
              : "#ffffff"
          }
          transparent
          opacity={isSelected ? 0.3 : hovered ? 0.2 : 0.1}
          emissive={isSelected ? moodConfig.color.split(" ")[1] : "#000000"}
          emissiveIntensity={isSelected ? 0.2 : 0}
        />
      </mesh>

      {/* Particle effect for selected emoji */}
      {isSelected && (
        <group>
          {[...Array(8)].map((_, i) => (
            <mesh
              key={i}
              position={[
                Math.cos((i / 8) * Math.PI * 2) * 1.5,
                Math.sin((i / 8) * Math.PI * 2) * 1.5,
                0,
              ]}
            >
              <sphereGeometry args={[0.02, 8, 8]} />
              <meshBasicMaterial color="#10b981" transparent opacity={0.6} />
            </mesh>
          ))}
        </group>
      )}
    </group>
  );
};

// 3D Scene Component
const MoodScene = ({ currentMood, onMoodChange }) => {
  const moods = [
    { mood: MOODS.SAD, position: [-3, 0, 0] },
    { mood: MOODS.HAPPY, position: [-1, 0, 0] },
    { mood: MOODS.NEUTRAL, position: [1, 0, 0] },
    { mood: MOODS.ANGRY, position: [3, 0, 0] },
  ];

  return (
    <>
      <ambientLight intensity={0.8} />
      <pointLight position={[10, 10, 10]} intensity={1.2} />
      <pointLight position={[-10, -10, -10]} intensity={0.7} color="#10b981" />
      <pointLight position={[0, 10, 5]} intensity={0.5} color="#3b82f6" />

      {moods.map(({ mood, position }) => (
        <EmojiMesh
          key={mood}
          emoji={MOOD_CONFIGS[mood].emoji}
          position={position}
          isSelected={currentMood === mood}
          onClick={() => onMoodChange(mood)}
          mood={mood}
          moodConfig={MOOD_CONFIGS[mood]}
        />
      ))}

      <OrbitControls
        enableZoom={false}
        enablePan={false}
        enableRotate={false}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 2}
        autoRotate={false}
      />
    </>
  );
};

const MoodSelector = () => {
  const { currentMood, changeMood, getCurrentMoodConfig, isTransitioning } =
    useMood();
  const [showDescription, setShowDescription] = useState(false);
  const currentConfig = getCurrentMoodConfig();

  const handleMoodChange = async (newMood) => {
    if (newMood !== currentMood) {
      await changeMood(newMood);
      setShowDescription(true);
      setTimeout(() => setShowDescription(false), 3000);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Title */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-3xl font-bold text-gradient mb-2">
          How are you feeling today?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Select your mood to receive guidance from the Quran
        </p>
      </motion.div>

      {/* 3D Mood Selector */}
      <div className="relative">
        <motion.div
          className={cn(
            "h-64 rounded-2xl glass-effect overflow-hidden",
            "border border-white/20 shadow-2xl",
            isTransitioning && "opacity-50"
          )}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <Canvas camera={{ position: [0, 0, 8], fov: 50 }}>
            <MoodScene
              currentMood={currentMood}
              onMoodChange={handleMoodChange}
            />
          </Canvas>
        </motion.div>

        {/* Mood Instructions */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center">
          <motion.div
            className="px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Click on the emojis to explore your feelings
            </p>
          </motion.div>
        </div>
      </div>

      {/* Current Mood Display */}
      <AnimatePresence>
        {currentConfig && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 text-center"
          >
            <div
              className={cn(
                "inline-flex items-center space-x-3 px-6 py-3 rounded-full",
                "glass-effect border border-white/20"
              )}
            >
              <span className="text-2xl">{currentConfig.emoji}</span>
              <div className="text-left">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                  {currentConfig.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentConfig.description}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mood Description Popup */}
      <AnimatePresence>
        {showDescription && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
          >
            <div className="glass-effect rounded-2xl p-6 max-w-md text-center border border-white/20">
              <div className="text-4xl mb-3">{currentConfig.emoji}</div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                {currentConfig.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {currentConfig.description}
              </p>
              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className={cn("h-full bg-gradient-to-r", currentConfig.color)}
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 3 }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MoodSelector;
