import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useMood, MOODS, MOOD_CONFIGS } from "../../contexts/MoodContext";
import { cn } from "../../utils/cn";

// Professional 3D Emoji Component with Realistic Continuous Animations
const EmojiButton = ({
  emoji,
  isSelected,
  onClick,
  mood,
  moodConfig,
  index,
}) => {
  const [hovered, setHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationTime, setAnimationTime] = useState(0);

  // Continuous animation loop for realistic movement
  useEffect(() => {
    const animate = () => {
      setAnimationTime(Date.now());
      requestAnimationFrame(animate);
    };
    animate();
  }, []);

  // Get realistic 3D emoji with continuous movement
  const getRealistic3DEmoji = () => {
    const time = animationTime * 0.001; // Convert to seconds
    const baseOffset = index * 0.5; // Offset each emoji's animation

    // Continuous breathing/floating animation
    const breathe = Math.sin(time * 2 + baseOffset) * 0.03;
    const float = Math.sin(time * 1.5 + baseOffset) * 2;
    const tilt = Math.sin(time * 0.8 + baseOffset) * 3;
    const rotate = Math.cos(time * 0.6 + baseOffset) * 2;

    // Enhanced movement when hovered or selected
    const hoverScale = hovered ? 1.2 : 1;
    const selectedScale = isSelected ? 1.1 : 1;
    const animatingScale = isAnimating ? 1.3 : 1;

    const finalScale =
      (1 + breathe) * hoverScale * selectedScale * animatingScale;

    // Dynamic emoji based on mood and state
    let currentEmoji = emoji;
    let emotionIntensity = 1;

    if (isAnimating) {
      emotionIntensity = 1.5;
      switch (mood) {
        case "sad":
          currentEmoji = "😭";
          break;
        case "happy":
          currentEmoji = "😆";
          break;
        case "angry":
          currentEmoji = "😡";
          break;
        case "peaceful":
          currentEmoji = "😇";
          break;
        default:
          currentEmoji = emoji;
      }
    } else if (hovered) {
      switch (mood) {
        case "sad":
          currentEmoji = "🥺";
          break;
        case "happy":
          currentEmoji = "😊";
          break;
        case "angry":
          currentEmoji = "😤";
          break;
        case "peaceful":
          currentEmoji = "😌";
          break;
        default:
          currentEmoji = emoji;
      }
    } else {
      // Subtle emotion cycling during idle
      const cycle = Math.floor(time * 0.3 + baseOffset) % 3;
      switch (mood) {
        case "sad":
          currentEmoji = cycle === 0 ? "😢" : cycle === 1 ? "😥" : "😞";
          break;
        case "happy":
          currentEmoji = cycle === 0 ? "😄" : cycle === 1 ? "🙂" : "😁";
          break;
        case "angry":
          currentEmoji = cycle === 0 ? "😠" : cycle === 1 ? "😡" : "😤";
          break;
        case "peaceful":
          currentEmoji = cycle === 0 ? "🕊️" : cycle === 1 ? "😌" : "😴";
          break;
        default:
          currentEmoji = cycle === 0 ? "😌" : cycle === 1 ? "🙂" : emoji;
      }
    }

    return {
      emoji: currentEmoji,
      transform: `
        perspective(1000px)
        translateY(${float}px)
        rotateX(${tilt}deg)
        rotateY(${rotate}deg)
        rotateZ(${Math.sin(time * 1.2 + baseOffset) * 1}deg)
        scale(${finalScale})
      `,
      filter: `
        drop-shadow(0 0 ${10 * emotionIntensity}px rgba(255, 255, 255, 0.6))
        drop-shadow(0 ${8 * emotionIntensity}px ${
        16 * emotionIntensity
      }px rgba(0, 0, 0, 0.3))
        brightness(${1 + emotionIntensity * 0.1})
        contrast(${1 + emotionIntensity * 0.2})
        saturate(${1 + emotionIntensity * 0.3})
      `,
      textShadow: `
        0 0 ${20 * emotionIntensity}px rgba(255, 255, 255, 0.8),
        0 0 ${40 * emotionIntensity}px rgba(255, 255, 255, 0.6),
        0 ${8 * emotionIntensity}px ${
        16 * emotionIntensity
      }px rgba(0, 0, 0, 0.4)
      `,
    };
  };

  const handleClick = () => {
    setIsAnimating(true);
    onClick();
    setTimeout(() => setIsAnimating(false), 1500);

    // Smooth scroll to guidance section after selection
    const el = document.getElementById("guidance");
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const emojiData = getRealistic3DEmoji();

  return (
    <motion.button
      onClick={handleClick}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      className={cn(
        "relative w-40 h-40 md:w-48 md:h-48 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer group",
        "shadow-2xl hover:shadow-3xl",
        "bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm border border-white/20",
        isSelected
          ? `ring-4 ring-emerald-400/50 shadow-emerald-400/25`
          : hovered
          ? "ring-2 ring-blue-400/50 shadow-blue-400/25"
          : ""
      )}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.98 }}
      style={{
        transformStyle: "preserve-3d",
        perspective: "1000px",
      }}
    >
      {/* Glowing background effect */}
      <div
        className={cn(
          "absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500",
          `bg-gradient-to-br ${moodConfig.color}`,
          "blur-2xl scale-125"
        )}
      />

      {/* Professional 3D Emoji with Realistic Movement */}
      <div
        className="relative z-10 select-none text-9xl md:text-[8rem]"
        style={{
          transform: emojiData.transform,
          filter: emojiData.filter,
          textShadow: emojiData.textShadow,
          transition: "all 0.1s ease-out",
          transformStyle: "preserve-3d",
        }}
      >
        {emojiData.emoji}
      </div>

      {/* Shadow under emoji for 3D depth */}
      <div className="emoji-shadow" />

      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center shadow-lg"
        >
          <span className="text-white text-xs">✓</span>
        </motion.div>
      )}

      {/* Particle effects */}
      {isSelected && (
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-emerald-400 rounded-full"
              style={{
                left: "50%",
                top: "50%",
                marginLeft: "-4px",
                marginTop: "-4px",
              }}
              animate={{
                x: Math.cos((i / 6) * Math.PI * 2) * 40,
                y: Math.sin((i / 6) * Math.PI * 2) * 40,
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.1,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      )}
    </motion.button>
  );
};

// Simple HTML Layout for Emojis
const MoodGrid = ({ currentMood, onMoodChange }) => {
  return (
    <div className="flex flex-wrap justify-center items-center gap-8 p-8 perspective-1000">
      {Object.values(MOODS).map((mood, index) => (
        <EmojiButton
          key={mood}
          emoji={MOOD_CONFIGS[mood].emoji}
          isSelected={currentMood === mood}
          onClick={() => onMoodChange(mood)}
          mood={mood}
          moodConfig={MOOD_CONFIGS[mood]}
          index={index}
        />
      ))}
    </div>
  );
};

const MoodSelector = () => {
  const { currentMood, changeMood, getCurrentMoodConfig, isTransitioning } =
    useMood();
  const [showDescription, setShowDescription] = useState(false);
  const currentConfig = getCurrentMoodConfig();

  const handleMoodChange = async (newMood) => {
    if (newMood !== currentMood) {
      await changeMood(newMood);
      setShowDescription(true);
      setTimeout(() => setShowDescription(false), 3000);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Title */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-3xl font-bold text-gradient mb-2">
          How are you feeling today?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Select your mood to receive guidance from the Quran
        </p>
      </motion.div>

      {/* Beautiful Mood Selector */}
      <div className="relative">
        <motion.div
          className={cn(
            "rounded-2xl glass-effect overflow-hidden",
            "border border-white/20 shadow-2xl p-4",
            isTransitioning && "opacity-50"
          )}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <MoodGrid currentMood={currentMood} onMoodChange={handleMoodChange} />
        </motion.div>

        {/* Mood Instructions */}
        <div className="mt-4 flex justify-center">
          <motion.div
            className="px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Click on the emojis to explore your feelings
            </p>
          </motion.div>
        </div>
      </div>

      {/* Current Mood Display */}
      <AnimatePresence>
        {currentConfig && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 text-center"
          >
            <div
              className={cn(
                "inline-flex items-center space-x-3 px-6 py-3 rounded-full",
                "glass-effect border border-white/20"
              )}
            >
              <span className="text-2xl">{currentConfig.emoji}</span>
              <div className="text-left">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                  {currentConfig.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentConfig.description}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mood Description Popup */}
      <AnimatePresence>
        {showDescription && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
          >
            <div className="glass-effect rounded-2xl p-6 max-w-md text-center border border-white/20">
              <div className="text-4xl mb-3">{currentConfig.emoji}</div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                {currentConfig.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {currentConfig.description}
              </p>
              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className={cn("h-full bg-gradient-to-r", currentConfig.color)}
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 3 }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MoodSelector;
