import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useMood, MOODS, MOOD_CONFIGS } from "../../contexts/MoodContext";
import { cn } from "../../utils/cn";

// Beautiful HTML Emoji Component with Animations
const EmojiButton = ({
  emoji,
  isSelected,
  onClick,
  mood,
  moodConfig,
  index,
}) => {
  const [hovered, setHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [tilt, setTilt] = useState({ rx: 0, ry: 0 });
  const [idleTick, setIdleTick] = useState(0);

  useEffect(() => {
    const id = setInterval(() => setIdleTick((t) => t + 1), 2000);
    return () => clearInterval(id);
  }, []);

  // Dynamic emoji expressions based on mood and interaction
  const getEmojiExpression = () => {
    const tickMod = idleTick % 3;
    if (isAnimating) {
      switch (mood) {
        case "sad":
          return "😭"; // More expressive when clicked
        case "happy":
          return "😆"; // More joyful when clicked
        case "angry":
          return "😡"; // More intense when clicked
        case "peaceful":
          return "😇"; // More serene when clicked
        default:
          return emoji;
      }
    }

    if (hovered) {
      switch (mood) {
        case "sad":
          return "🥺"; // Slightly more sad when hovered
        case "happy":
          return "😊"; // Gentle smile when hovered
        case "angry":
          return "😤"; // Frustrated when hovered
        case "peaceful":
          return "😌"; // Calm when hovered
        default:
          return emoji;
      }
    }

    // Idle subtle emotion cycling
    switch (mood) {
      case "sad":
        return tickMod === 0 ? "😢" : tickMod === 1 ? "😥" : "😞";
      case "happy":
        return tickMod === 0 ? "😄" : tickMod === 1 ? "🙂" : "😁";
      case "angry":
        return tickMod === 0 ? "😠" : tickMod === 1 ? "😡" : "😤";
      case "peaceful":
        return tickMod === 0 ? "🕊️" : tickMod === 1 ? "😌" : "😴";
      case "neutral":
      default:
        return tickMod === 0 ? "😌" : tickMod === 1 ? "🙂" : emoji;
    }
  };

  const onMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const midX = rect.width / 2;
    const midY = rect.height / 2;
    const ry = ((x - midX) / midX) * 10; // rotateY
    const rx = -((y - midY) / midY) * 10; // rotateX
    setTilt({ rx, ry });
  };

  const onMouseLeave = () => {
    setHovered(false);
    setTilt({ rx: 0, ry: 0 });
  };

  const handleClick = () => {
    setIsAnimating(true);
    onClick();
    setTimeout(() => setIsAnimating(false), 1000);

    // Smooth scroll to guidance section after selection
    const el = document.getElementById("guidance");
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  return (
    <motion.button
      onClick={handleClick}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={onMouseLeave}
      onMouseMove={onMouseMove}
      className={cn(
        "relative w-28 h-28 md:w-32 md:h-32 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer group btn-3d has-shine emoji-3d preserve-3d",
        "shadow-lg hover:shadow-2xl",
        isSelected
          ? `bg-gradient-to-br ${moodConfig.color} shadow-2xl ring-4 ring-white/30`
          : hovered
          ? "bg-gradient-to-br from-emerald-400 to-blue-500 shadow-xl"
          : "bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800"
      )}
      style={{ transform: `rotateX(${tilt.rx}deg) rotateY(${tilt.ry}deg)` }}
      whileHover={{ scale: 1.08, y: -6 }}
      whileTap={{ scale: 0.95 }}
      animate={{
        scale: isSelected ? [1, 1.05, 1] : 1,
        y: isAnimating ? [0, -10, 0] : [0, -2, 0],
        rotate: isAnimating ? [0, 8, -8, 0] : [0, 1, -1, 0],
      }}
      transition={{
        duration: isAnimating ? 1 : 2.2,
        repeat: Infinity,
        repeatDuration: 2,
        ease: "easeInOut",
      }}
      style={{
        animationDelay: `${index * 0.1}s`,
      }}
    >
      {/* Glowing background effect */}
      <div
        className={cn(
          "absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300",
          `bg-gradient-to-br ${moodConfig.color}`,
          "blur-xl scale-110"
        )}
      />

      {/* Emoji */}
      <motion.span
        className="text-5xl md:text-6xl relative z-10 select-none"
        animate={{
          scale: isAnimating ? [1, 1.3, 1] : [1, 1.05, 1],
          rotate: isAnimating ? [0, 15, -15, 0] : [0, 2, -2, 0],
        }}
        transition={{ duration: 0.3 }}
        style={{
          filter: isSelected
            ? "drop-shadow(0 0 10px rgba(255,255,255,0.8))"
            : "none",
        }}
      >
        {getEmojiExpression()}
      </motion.span>

      {/* Shadow under emoji for 3D depth */}
      <div className="emoji-shadow" />

      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center shadow-lg"
        >
          <span className="text-white text-xs">✓</span>
        </motion.div>
      )}

      {/* Particle effects */}
      {isSelected && (
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-emerald-400 rounded-full"
              style={{
                left: "50%",
                top: "50%",
                marginLeft: "-4px",
                marginTop: "-4px",
              }}
              animate={{
                x: Math.cos((i / 6) * Math.PI * 2) * 40,
                y: Math.sin((i / 6) * Math.PI * 2) * 40,
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.1,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      )}
    </motion.button>
  );
};

// Simple HTML Layout for Emojis
const MoodGrid = ({ currentMood, onMoodChange }) => {
  return (
    <div className="flex flex-wrap justify-center items-center gap-8 p-8 perspective-1000">
      {Object.values(MOODS).map((mood, index) => (
        <EmojiButton
          key={mood}
          emoji={MOOD_CONFIGS[mood].emoji}
          isSelected={currentMood === mood}
          onClick={() => onMoodChange(mood)}
          mood={mood}
          moodConfig={MOOD_CONFIGS[mood]}
          index={index}
        />
      ))}
    </div>
  );
};

const MoodSelector = () => {
  const { currentMood, changeMood, getCurrentMoodConfig, isTransitioning } =
    useMood();
  const [showDescription, setShowDescription] = useState(false);
  const currentConfig = getCurrentMoodConfig();

  const handleMoodChange = async (newMood) => {
    if (newMood !== currentMood) {
      await changeMood(newMood);
      setShowDescription(true);
      setTimeout(() => setShowDescription(false), 3000);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Title */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-3xl font-bold text-gradient mb-2">
          How are you feeling today?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Select your mood to receive guidance from the Quran
        </p>
      </motion.div>

      {/* Beautiful Mood Selector */}
      <div className="relative">
        <motion.div
          className={cn(
            "rounded-2xl glass-effect overflow-hidden",
            "border border-white/20 shadow-2xl p-4",
            isTransitioning && "opacity-50"
          )}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <MoodGrid currentMood={currentMood} onMoodChange={handleMoodChange} />
        </motion.div>

        {/* Mood Instructions */}
        <div className="mt-4 flex justify-center">
          <motion.div
            className="px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Click on the emojis to explore your feelings
            </p>
          </motion.div>
        </div>
      </div>

      {/* Current Mood Display */}
      <AnimatePresence>
        {currentConfig && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 text-center"
          >
            <div
              className={cn(
                "inline-flex items-center space-x-3 px-6 py-3 rounded-full",
                "glass-effect border border-white/20"
              )}
            >
              <span className="text-2xl">{currentConfig.emoji}</span>
              <div className="text-left">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                  {currentConfig.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentConfig.description}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mood Description Popup */}
      <AnimatePresence>
        {showDescription && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
          >
            <div className="glass-effect rounded-2xl p-6 max-w-md text-center border border-white/20">
              <div className="text-4xl mb-3">{currentConfig.emoji}</div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                {currentConfig.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {currentConfig.description}
              </p>
              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className={cn("h-full bg-gradient-to-r", currentConfig.color)}
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 3 }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MoodSelector;
