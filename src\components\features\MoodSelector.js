import React, { useState, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Text, OrbitControls, Float } from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import { useMood, MOODS, MOOD_CONFIGS } from '../../contexts/MoodContext';
import { cn } from '../../utils/cn';

// 3D Emoji Component
const EmojiMesh = ({ emoji, position, isSelected, onClick, mood }) => {
  const meshRef = useRef();
  const [hovered, setHovered] = useState(false);
  
  useFrame((state) => {
    if (meshRef.current) {
      // Floating animation
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      
      // Rotation based on selection and hover
      if (isSelected) {
        meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;
      } else if (hovered) {
        meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 3) * 0.2;
      }
      
      // Scale animation
      const targetScale = isSelected ? 1.3 : hovered ? 1.1 : 1;
      meshRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale }, 0.1);
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <mesh
        ref={meshRef}
        position={position}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        scale={isSelected ? 1.3 : 1}
      >
        <sphereGeometry args={[0.8, 32, 32]} />
        <meshStandardMaterial
          color={isSelected ? '#10b981' : hovered ? '#34d399' : '#ffffff'}
          transparent
          opacity={0.1}
        />
        <Text
          position={[0, 0, 0.1]}
          fontSize={1.5}
          color={isSelected ? '#10b981' : '#374151'}
          anchorX="center"
          anchorY="middle"
        >
          {emoji}
        </Text>
      </mesh>
    </Float>
  );
};

// 3D Scene Component
const MoodScene = ({ currentMood, onMoodChange }) => {
  const moods = [
    { mood: MOODS.SAD, position: [-3, 0, 0] },
    { mood: MOODS.HAPPY, position: [-1, 0, 0] },
    { mood: MOODS.NEUTRAL, position: [1, 0, 0] },
    { mood: MOODS.ANGRY, position: [3, 0, 0] },
  ];

  return (
    <>
      <ambientLight intensity={0.6} />
      <pointLight position={[10, 10, 10]} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#10b981" />
      
      {moods.map(({ mood, position }) => (
        <EmojiMesh
          key={mood}
          emoji={MOOD_CONFIGS[mood].emoji}
          position={position}
          isSelected={currentMood === mood}
          onClick={() => onMoodChange(mood)}
          mood={mood}
        />
      ))}
      
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 2}
        autoRotate
        autoRotateSpeed={0.5}
      />
    </>
  );
};

const MoodSelector = () => {
  const { currentMood, changeMood, getCurrentMoodConfig, isTransitioning } = useMood();
  const [showDescription, setShowDescription] = useState(false);
  const currentConfig = getCurrentMoodConfig();

  const handleMoodChange = async (newMood) => {
    if (newMood !== currentMood) {
      await changeMood(newMood);
      setShowDescription(true);
      setTimeout(() => setShowDescription(false), 3000);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Title */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h2 className="text-3xl font-bold text-gradient mb-2">
          How are you feeling today?
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Select your mood to receive guidance from the Quran
        </p>
      </motion.div>

      {/* 3D Mood Selector */}
      <div className="relative">
        <motion.div
          className={cn(
            "h-64 rounded-2xl glass-effect overflow-hidden",
            "border border-white/20 shadow-2xl",
            isTransitioning && "opacity-50"
          )}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          <Canvas camera={{ position: [0, 0, 8], fov: 50 }}>
            <MoodScene currentMood={currentMood} onMoodChange={handleMoodChange} />
          </Canvas>
        </motion.div>

        {/* Mood Labels */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-8">
          {Object.entries(MOOD_CONFIGS).slice(0, 4).map(([mood, config]) => (
            <motion.button
              key={mood}
              onClick={() => handleMoodChange(mood)}
              className={cn(
                "px-3 py-1 rounded-full text-xs font-medium transition-all",
                "bg-white/20 hover:bg-white/30 backdrop-blur-sm",
                "border border-white/20",
                currentMood === mood && "bg-emerald-500/30 border-emerald-400/50"
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {config.name}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Current Mood Display */}
      <AnimatePresence>
        {currentConfig && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 text-center"
          >
            <div className={cn(
              "inline-flex items-center space-x-3 px-6 py-3 rounded-full",
              "glass-effect border border-white/20"
            )}>
              <span className="text-2xl">{currentConfig.emoji}</span>
              <div className="text-left">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                  {currentConfig.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {currentConfig.description}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mood Description Popup */}
      <AnimatePresence>
        {showDescription && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
          >
            <div className="glass-effect rounded-2xl p-6 max-w-md text-center border border-white/20">
              <div className="text-4xl mb-3">{currentConfig.emoji}</div>
              <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                {currentConfig.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {currentConfig.description}
              </p>
              <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  className={cn("h-full bg-gradient-to-r", currentConfig.color)}
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 3 }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MoodSelector;
