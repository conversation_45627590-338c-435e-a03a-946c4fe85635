import React, { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, Book, Heart, Copy, Check } from "lucide-react";
import { cn } from "../../utils/cn";
import AudioPlayer from "../ui/AudioPlayer";
import audioService from "../../services/audioService";

const QuranSearch = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [favorites, setFavorites] = useState([]);
  const [copiedVerse, setCopiedVerse] = useState(null);

  // Sample Quran verses for search (in a real app, this would be a complete database)
  const quranVerses = [
    {
      id: 1,
      surah: 1,
      ayah: 1,
      arabic: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
      english:
        "In the name of <PERSON>, the Entirely Merciful, the Especially Merciful.",
      surahName: "Al-Fatiha",
      keywords: ["bismillah", "allah", "merciful", "name", "rahman", "raheem"],
    },
    {
      id: 2,
      surah: 1,
      ayah: 2,
      arabic: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
      english: "All praise is due to Allah, Lord of the worlds.",
      surahName: "Al-Fatiha",
      keywords: [
        "praise",
        "allah",
        "lord",
        "worlds",
        "hamd",
        "rabb",
        "alameen",
      ],
    },
    {
      id: 3,
      surah: 2,
      ayah: 255,
      arabic: "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ",
      english:
        "Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence.",
      surahName: "Al-Baqarah",
      keywords: [
        "allah",
        "deity",
        "living",
        "sustainer",
        "ayat",
        "kursi",
        "throne",
      ],
    },
    {
      id: 4,
      surah: 3,
      ayah: 26,
      arabic: "قُلِ اللَّهُمَّ مَالِكَ الْمُلْكِ تُؤْتِي الْمُلْكَ مَن تَشَاءُ",
      english:
        'Say, "O Allah, Owner of Sovereignty, You give sovereignty to whom You will"',
      surahName: "Ali Imran",
      keywords: ["allah", "sovereignty", "owner", "kingdom", "malik", "mulk"],
    },
    {
      id: 5,
      surah: 24,
      ayah: 35,
      arabic: "اللَّهُ نُورُ السَّمَاوَاتِ وَالْأَرْضِ",
      english: "Allah is the light of the heavens and the earth.",
      surahName: "An-Nur",
      keywords: ["allah", "light", "heavens", "earth", "nur", "samawat", "ard"],
    },
    {
      id: 6,
      surah: 55,
      ayah: 13,
      arabic: "فَبِأَيِّ آلَاءِ رَبِّكُمَا تُكَذِّبَانِ",
      english: "So which of the favors of your Lord would you deny?",
      surahName: "Ar-Rahman",
      keywords: ["favors", "lord", "deny", "blessings", "alaa", "rabbikuma"],
    },
    {
      id: 7,
      surah: 67,
      ayah: 2,
      arabic:
        "الَّذِي خَلَقَ الْمَوْتَ وَالْحَيَاةَ لِيَبْلُوَكُمْ أَيُّكُمْ أَحْسَنُ عَمَلًا",
      english:
        "Who created death and life to test you as to which of you is best in deed.",
      surahName: "Al-Mulk",
      keywords: [
        "created",
        "death",
        "life",
        "test",
        "deed",
        "khalaq",
        "mawt",
        "hayat",
      ],
    },
    {
      id: 8,
      surah: 112,
      ayah: 1,
      arabic: "قُلْ هُوَ اللَّهُ أَحَدٌ",
      english: 'Say, "He is Allah, [who is] One"',
      surahName: "Al-Ikhlas",
      keywords: ["allah", "one", "unity", "ahad", "ikhlas"],
    },
  ];

  useEffect(() => {
    const saved = localStorage.getItem("quran-search-favorites");
    if (saved) {
      setFavorites(JSON.parse(saved));
    }
  }, []);

  const performSearch = (term) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    // Simulate API delay
    setTimeout(() => {
      const results = quranVerses.filter(
        (verse) =>
          verse.english.toLowerCase().includes(term.toLowerCase()) ||
          verse.keywords.some((keyword) =>
            keyword.toLowerCase().includes(term.toLowerCase())
          ) ||
          verse.surahName.toLowerCase().includes(term.toLowerCase())
      );

      setSearchResults(results);
      setIsSearching(false);
    }, 500);
  };

  // Debounced input (faster)
  const [debouncedTerm, setDebouncedTerm] = useState("");
  useEffect(() => {
    const id = setTimeout(() => setDebouncedTerm(searchTerm), 200);
    return () => clearTimeout(id);
  }, [searchTerm]);

  useEffect(() => {
    performSearch(debouncedTerm);
  }, [debouncedTerm]);

  const handleSearch = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
  };

  const toggleFavorite = (verseId) => {
    const newFavorites = favorites.includes(verseId)
      ? favorites.filter((fav) => fav !== verseId)
      : [...favorites, verseId];

    setFavorites(newFavorites);
    localStorage.setItem(
      "quran-search-favorites",
      JSON.stringify(newFavorites)
    );
  };

  const copyVerse = async (verse) => {
    const text = `${verse.arabic}\n\n"${verse.english}"\n\n- Quran ${verse.surah}:${verse.ayah} (${verse.surahName})`;

    try {
      await navigator.clipboard.writeText(text);
      setCopiedVerse(verse.id);
      setTimeout(() => setCopiedVerse(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-gray-700/50"
    >
      {/* Header + New Input */}
      <div className="mb-6">
        <div className="flex items-center justify-center mb-4">
          <motion.div
            className="flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
          >
            <Book className="w-6 h-6 text-emerald-500" />
            <h3 className="text-xl font-bold text-gray-800 dark:text-white">
              Quran Search
            </h3>
          </motion.div>
        </div>

        {/* Rebuilt search bar */}
        <div className="flex items-center gap-2">
          <div className="flex-1 flex items-center bg-white/70 dark:bg-gray-800/70 border border-gray-200/50 dark:border-gray-600/50 rounded-xl px-3 py-2 shadow-sm">
            <Search className="w-5 h-5 text-gray-400 mr-2" />
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearch}
              placeholder="Search Quran verses, keywords, or surah names..."
              className="flex-1 bg-transparent outline-none text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm("")}
              className="btn btn-ghost btn-3d"
            >
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Search Results */}
      <AnimatePresence>
        {isSearching ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-8"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Searching...</p>
          </motion.div>
        ) : searchTerm && searchResults.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-8"
          >
            <Book className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No verses found for "{searchTerm}"
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              Try searching for keywords like "mercy", "guidance", or "peace"
            </p>
          </motion.div>
        ) : searchResults.length > 0 ? (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {searchResults.map((verse, index) => (
              <motion.div
                key={verse.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 rounded-xl bg-white/50 dark:bg-gray-700/40 border border-gray-200/40 dark:border-gray-600/30 hover:bg-white/70 dark:hover:bg-gray-600/50 transition-all shadow-sm"
              >
                {/* Verse Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 rounded-full bg-emerald-500/15 text-emerald-700 dark:text-emerald-400 border border-emerald-500/30 text-xs font-medium">
                      {verse.surahName} {verse.surah}:{verse.ayah}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <motion.button
                      onClick={() => toggleFavorite(verse.id)}
                      className={cn(
                        "p-2 rounded-full transition-colors btn-icon btn-ghost",
                        favorites.includes(verse.id)
                          ? "text-red-500"
                          : "text-gray-500 hover:text-red-500"
                      )}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Heart
                        className={cn(
                          "w-4 h-4",
                          favorites.includes(verse.id) && "fill-current"
                        )}
                      />
                    </motion.button>

                    <motion.button
                      onClick={() => copyVerse(verse)}
                      className="p-2 rounded-full text-gray-500 hover:text-emerald-500 transition-colors btn-icon btn-ghost"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {copiedVerse === verse.id ? (
                        <Check className="w-4 h-4 text-emerald-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </motion.button>
                  </div>
                </div>

                {/* Arabic Text */}
                <div className="text-right mb-3">
                  <p className="text-lg font-arabic text-gray-800 dark:text-white leading-relaxed">
                    {verse.arabic}
                  </p>
                </div>

                {/* English Translation */}
                <div className="mb-3">
                  <p className="text-gray-700 dark:text-gray-300 italic">
                    "{verse.english}"
                  </p>
                </div>

                {/* Audio Player + Listen Button */}
                <div className="mt-3 flex items-center justify-between">
                  <AudioPlayer
                    surah={verse.surah}
                    ayah={verse.ayah}
                    showVolumeControl={false}
                    className="flex-1"
                  />
                  <button
                    className="ml-3 btn btn-primary btn-3d"
                    onClick={() => {
                      if (navigator?.vibrate) navigator.vibrate(10);
                      audioService.playVerse(
                        verse.surah,
                        verse.ayah,
                        "luhaidan"
                      );
                    }}
                    title="Click to listen to recitation"
                  >
                    Listen
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        ) : null}
      </AnimatePresence>

      {/* Removed Popular searches */}
    </motion.div>
  );
};

export default QuranSearch;
