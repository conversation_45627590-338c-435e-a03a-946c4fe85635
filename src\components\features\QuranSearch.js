import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Book,
  Heart,
  Copy,
  Check,
  Play,
  Pause,
  Volume2,
  BookOpen,
  X,
} from "lucide-react";
import { cn } from "../../utils/cn";
import audioService from "../../services/audioService";

const QuranSearch = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [favorites, setFavorites] = useState([]);
  const [copiedVerse, setCopiedVerse] = useState(null);
  const [playingVerse, setPlayingVerse] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showQuranReader, setShowQuranReader] = useState(false);

  // Sample Quran verses for search (in a real app, this would be a complete database)
  const quranVerses = [
    {
      id: 1,
      surah: 1,
      ayah: 1,
      arabic: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
      english:
        "In the name of Allah, the Entirely Merciful, the Especially Merciful.",
      surahName: "Al-Fatiha",
      keywords: ["bismillah", "allah", "merciful", "name", "rahman", "raheem"],
    },
    {
      id: 2,
      surah: 1,
      ayah: 2,
      arabic: "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
      english: "All praise is due to Allah, Lord of the worlds.",
      surahName: "Al-Fatiha",
      keywords: [
        "praise",
        "allah",
        "lord",
        "worlds",
        "hamd",
        "rabb",
        "alameen",
      ],
    },
    {
      id: 3,
      surah: 2,
      ayah: 255,
      arabic: "اللَّهُ لَا إِلَٰهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ",
      english:
        "Allah - there is no deity except Him, the Ever-Living, the Sustainer of existence.",
      surahName: "Al-Baqarah",
      keywords: [
        "allah",
        "deity",
        "living",
        "sustainer",
        "ayat",
        "kursi",
        "throne",
      ],
    },
    {
      id: 4,
      surah: 3,
      ayah: 26,
      arabic: "قُلِ اللَّهُمَّ مَالِكَ الْمُلْكِ تُؤْتِي الْمُلْكَ مَن تَشَاءُ",
      english:
        'Say, "O Allah, Owner of Sovereignty, You give sovereignty to whom You will"',
      surahName: "Ali Imran",
      keywords: ["allah", "sovereignty", "owner", "kingdom", "malik", "mulk"],
    },
    {
      id: 5,
      surah: 24,
      ayah: 35,
      arabic: "اللَّهُ نُورُ السَّمَاوَاتِ وَالْأَرْضِ",
      english: "Allah is the light of the heavens and the earth.",
      surahName: "An-Nur",
      keywords: ["allah", "light", "heavens", "earth", "nur", "samawat", "ard"],
    },
    {
      id: 6,
      surah: 55,
      ayah: 13,
      arabic: "فَبِأَيِّ آلَاءِ رَبِّكُمَا تُكَذِّبَانِ",
      english: "So which of the favors of your Lord would you deny?",
      surahName: "Ar-Rahman",
      keywords: ["favors", "lord", "deny", "blessings", "alaa", "rabbikuma"],
    },
    {
      id: 7,
      surah: 67,
      ayah: 2,
      arabic:
        "الَّذِي خَلَقَ الْمَوْتَ وَالْحَيَاةَ لِيَبْلُوَكُمْ أَيُّكُمْ أَحْسَنُ عَمَلًا",
      english:
        "Who created death and life to test you as to which of you is best in deed.",
      surahName: "Al-Mulk",
      keywords: [
        "created",
        "death",
        "life",
        "test",
        "deed",
        "khalaq",
        "mawt",
        "hayat",
      ],
    },
    {
      id: 8,
      surah: 112,
      ayah: 1,
      arabic: "قُلْ هُوَ اللَّهُ أَحَدٌ",
      english: 'Say, "He is Allah, [who is] One"',
      surahName: "Al-Ikhlas",
      keywords: ["allah", "one", "unity", "ahad", "ikhlas"],
    },
  ];

  useEffect(() => {
    const saved = localStorage.getItem("quran-search-favorites");
    if (saved) {
      setFavorites(JSON.parse(saved));
    }

    // Set up audio service listeners
    audioService.onPlayStateChange = (playing) => {
      setIsPlaying(playing);
    };

    return () => {
      audioService.stop();
    };
  }, []);

  const performSearch = (term) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    // Simulate API delay
    setTimeout(() => {
      const results = quranVerses.filter(
        (verse) =>
          verse.english.toLowerCase().includes(term.toLowerCase()) ||
          verse.keywords.some((keyword) =>
            keyword.toLowerCase().includes(term.toLowerCase())
          ) ||
          verse.surahName.toLowerCase().includes(term.toLowerCase())
      );

      setSearchResults(results);
      setIsSearching(false);
    }, 500);
  };

  // Debounced input
  const [debouncedTerm, setDebouncedTerm] = useState("");
  useEffect(() => {
    const id = setTimeout(() => setDebouncedTerm(searchTerm), 300);
    return () => clearTimeout(id);
  }, [searchTerm]);

  useEffect(() => {
    performSearch(debouncedTerm);
  }, [debouncedTerm]);

  const handleSearch = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
  };

  const toggleFavorite = (verseId) => {
    const newFavorites = favorites.includes(verseId)
      ? favorites.filter((fav) => fav !== verseId)
      : [...favorites, verseId];

    setFavorites(newFavorites);
    localStorage.setItem(
      "quran-search-favorites",
      JSON.stringify(newFavorites)
    );
  };

  const copyVerse = async (verse) => {
    const text = `${verse.arabic}\n\n"${verse.english}"\n\n- Quran ${verse.surah}:${verse.ayah} (${verse.surahName})`;

    try {
      await navigator.clipboard.writeText(text);
      setCopiedVerse(verse.id);
      setTimeout(() => setCopiedVerse(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const playVerse = async (verse) => {
    try {
      if (playingVerse === verse.id && isPlaying) {
        audioService.pause();
        setPlayingVerse(null);
      } else {
        setPlayingVerse(verse.id);
        await audioService.playVerse(verse.surah, verse.ayah, "luhaidan");
      }
    } catch (error) {
      console.error("Failed to play verse:", error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Modern Search Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-emerald-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
          Explore the Quran
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Search through verses, discover wisdom, and listen to beautiful
          recitations
        </p>
      </motion.div>

      {/* Professional Centered Search Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="relative max-w-3xl mx-auto"
      >
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 via-blue-500 to-purple-600 rounded-2xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div className="relative bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl border border-white/30 dark:border-gray-700/50 shadow-2xl">
            <div className="flex items-center p-6">
              <Search className="w-6 h-6 text-gray-400 mr-4 flex-shrink-0" />
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearch}
                placeholder="Search Quran verses, keywords, or surah names..."
                className="flex-1 bg-transparent text-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none placeholder-sm"
              />
              {isSearching && (
                <div className="ml-4 w-6 h-6 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex flex-wrap justify-center gap-4"
      >
        <button
          onClick={() => setShowQuranReader(true)}
          className="btn btn-primary flex items-center gap-2 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <BookOpen className="w-5 h-5" />
          Full Quran Reader
        </button>
      </motion.div>

      {/* Modern Search Results */}
      <AnimatePresence>
        {isSearching ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-12"
          >
            <div className="relative">
              <div className="w-16 h-16 border-4 border-emerald-200 dark:border-emerald-800 rounded-full mx-auto mb-6"></div>
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-16 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Searching the Quran...
            </p>
          </motion.div>
        ) : searchTerm && searchResults.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-12"
          >
            <Book className="w-16 h-16 text-gray-400 mx-auto mb-6" />
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-2">
              No verses found for "{searchTerm}"
            </p>
            <p className="text-gray-500 dark:text-gray-400">
              Try searching for keywords like "mercy", "guidance", or "peace"
            </p>
          </motion.div>
        ) : searchResults.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid gap-6 max-w-6xl mx-auto"
          >
            {searchResults.map((verse, index) => (
              <motion.div
                key={verse.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="group relative"
              >
                {/* Gradient Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/10 via-blue-500/10 to-purple-600/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Main Card */}
                <div className="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-3xl border border-white/30 dark:border-gray-700/50 shadow-2xl p-8 hover:shadow-3xl transition-all duration-500">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="px-4 py-2 rounded-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white text-sm font-semibold shadow-lg">
                        {verse.surahName} {verse.surah}:{verse.ayah}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Favorite Button */}
                      <motion.button
                        onClick={() => toggleFavorite(verse.id)}
                        className={cn(
                          "p-3 rounded-full transition-all duration-300 shadow-lg",
                          favorites.includes(verse.id)
                            ? "bg-red-500 text-white shadow-red-500/25"
                            : "bg-white/80 dark:bg-gray-700/80 text-gray-600 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-500"
                        )}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Heart
                          className={cn(
                            "w-5 h-5",
                            favorites.includes(verse.id) && "fill-current"
                          )}
                        />
                      </motion.button>

                      {/* Copy Button */}
                      <motion.button
                        onClick={() => copyVerse(verse)}
                        className="p-3 rounded-full bg-white/80 dark:bg-gray-700/80 text-gray-600 dark:text-gray-300 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 hover:text-emerald-500 transition-all duration-300 shadow-lg"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        {copiedVerse === verse.id ? (
                          <Check className="w-5 h-5 text-emerald-500" />
                        ) : (
                          <Copy className="w-5 h-5" />
                        )}
                      </motion.button>
                    </div>
                  </div>

                  {/* Arabic Text */}
                  <div className="text-right mb-6">
                    <p className="text-2xl md:text-3xl font-arabic text-gray-900 dark:text-white leading-relaxed">
                      {verse.arabic}
                    </p>
                  </div>

                  {/* English Translation */}
                  <div className="mb-6">
                    <p className="text-lg md:text-xl text-gray-700 dark:text-gray-200 italic leading-relaxed">
                      "{verse.english}"
                    </p>
                  </div>

                  {/* Audio Controls */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
                    <motion.button
                      onClick={() => playVerse(verse)}
                      className={cn(
                        "flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg",
                        playingVerse === verse.id && isPlaying
                          ? "bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-red-500/25"
                          : "bg-gradient-to-r from-emerald-500 to-blue-500 text-white shadow-emerald-500/25 hover:shadow-emerald-500/40"
                      )}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {playingVerse === verse.id && isPlaying ? (
                        <Pause className="w-5 h-5" />
                      ) : (
                        <Play className="w-5 h-5" />
                      )}
                      {playingVerse === verse.id && isPlaying
                        ? "Pause Recitation"
                        : "Listen to Recitation"}
                    </motion.button>

                    <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                      <Volume2 className="w-4 h-4" />
                      <span>Muhammad Al-Luhaidan</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        ) : null}
      </AnimatePresence>

      {/* Working Quran Reader Modal */}
      <AnimatePresence>
        {showQuranReader && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowQuranReader(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-3xl p-8 max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-3xl font-bold bg-gradient-to-r from-emerald-500 to-blue-500 bg-clip-text text-transparent">
                  Full Quran Reader
                </h3>
                <button
                  onClick={() => setShowQuranReader(false)}
                  className="p-3 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors shadow-lg"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Quran Reader Content */}
              <div className="space-y-8">
                <div className="text-center py-12 bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 rounded-2xl">
                  <BookOpen className="w-20 h-20 text-emerald-500 mx-auto mb-6" />
                  <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Interactive Quran Reader
                  </h4>
                  <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                    Experience the complete Quran with beautiful Arabic text,
                    translations, and audio recitations by Muhammad Al-Luhaidan.
                  </p>

                  {/* Sample Surahs */}
                  <div className="grid md:grid-cols-2 gap-6 mt-8">
                    {quranVerses.slice(0, 4).map((verse) => (
                      <div
                        key={verse.id}
                        className="bg-white/80 dark:bg-gray-800/80 rounded-xl p-6 shadow-lg"
                      >
                        <div className="text-right mb-4">
                          <p className="text-xl font-arabic text-gray-900 dark:text-white leading-relaxed">
                            {verse.arabic}
                          </p>
                        </div>
                        <div className="mb-4">
                          <p className="text-gray-700 dark:text-gray-300 italic">
                            "{verse.english}"
                          </p>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-semibold text-emerald-600 dark:text-emerald-400">
                            {verse.surahName} {verse.surah}:{verse.ayah}
                          </span>
                          <button
                            onClick={() => playVerse(verse)}
                            className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors"
                          >
                            <Play className="w-4 h-4" />
                            Listen
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default QuranSearch;
