import React from "react";
import { ThemeProvider } from "./contexts/ThemeContext";
import { MoodProvider } from "./contexts/MoodContext";
import Layout from "./components/layout/Layout";
import MoodSelector from "./components/features/MoodSelector";
import AyahDisplay from "./components/features/AyahDisplay";
import PrayerTimes from "./components/features/PrayerTimes";
import DailyHadith from "./components/features/DailyHadith";
import IslamicQuotes from "./components/features/IslamicQuotes";
import { motion } from "framer-motion";

function App() {
  return (
    <ThemeProvider>
      <MoodProvider>
        <Layout>
          <div className="space-y-8">
            {/* Hero Section with Mood Selector */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center py-8"
            >
              <MoodSelector />
            </motion.section>

            {/* Ayah Display */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <AyahDisplay />
            </motion.section>

            {/* Islamic Features Grid */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
            >
              <PrayerTimes />
              <DailyHadith />
              <IslamicQuotes />
            </motion.section>

            {/* Footer */}
            <motion.footer
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-center py-8 text-gray-600 dark:text-gray-400"
            >
              <p className="mb-2">
                May Allah guide us all to the straight path
              </p>
              <p className="text-sm">Built with ❤️ for the Muslim community</p>
            </motion.footer>
          </div>
        </Layout>
      </MoodProvider>
    </ThemeProvider>
  );
}

export default App;
