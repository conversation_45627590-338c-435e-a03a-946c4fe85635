import React from "react";
import { ThemeProvider } from "./contexts/ThemeContext";
import { MoodProvider } from "./contexts/MoodContext";
import Layout from "./components/layout/Layout";
import MoodSelector from "./components/features/MoodSelector";
import AyahDisplay from "./components/features/AyahDisplay";
import PrayerTimes from "./components/features/PrayerTimes";
import DailyHadith from "./components/features/DailyHadith";
import IslamicQuotes from "./components/features/IslamicQuotes";
import DhikrCounter from "./components/features/DhikrCounter";
import QiblaDirection from "./components/features/QiblaDirection";
import IslamicCalendar from "./components/features/IslamicCalendar";
import Names<PERSON>fAllah from "./components/features/NamesOfAllah";
import QuranSearch from "./components/features/QuranSearch";
import AnimatedBackground from "./components/ui/AnimatedBackground";
import { motion } from "framer-motion";

function App() {
  return (
    <ThemeProvider>
      <MoodProvider>
        <AnimatedBackground />
        <Layout>
          <div className="space-y-8">
            {/* Hero Section with Mood Selector */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center py-8"
            >
              <MoodSelector />
            </motion.section>

            {/* Ayah Display */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <AyahDisplay />
            </motion.section>

            {/* Islamic Features Grid */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
            >
              <PrayerTimes />
              <DailyHadith />
              <IslamicQuotes />
            </motion.section>

            {/* Additional Islamic Features */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {/* Dhikr Counter */}
              <motion.section
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <DhikrCounter />
              </motion.section>

              {/* Qibla Direction */}
              <motion.section
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                <QiblaDirection />
              </motion.section>

              {/* Islamic Calendar */}
              <motion.section
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              >
                <IslamicCalendar />
              </motion.section>

              {/* Names of Allah */}
              <motion.section
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9 }}
                className="lg:col-span-2 xl:col-span-3"
              >
                <NamesOfAllah />
              </motion.section>

              {/* Quran Search */}
              <motion.section
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.0 }}
                className="lg:col-span-2 xl:col-span-3"
              >
                <QuranSearch />
              </motion.section>
            </div>

            {/* Footer */}
            <motion.footer
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-center py-8 text-gray-600 dark:text-gray-400"
            >
              <p className="mb-2">
                May Allah guide us all to the straight path
              </p>
              <p className="text-sm">Built with ❤️ for the Muslim community</p>
            </motion.footer>
          </div>
        </Layout>
      </MoodProvider>
    </ThemeProvider>
  );
}

export default App;
