import React from "react";
import { ThemeProvider } from "./contexts/ThemeContext";
import { MoodProvider } from "./contexts/MoodContext";
import Layout from "./components/layout/Layout";
import MoodSelector from "./components/features/MoodSelector";
import AyahDisplay from "./components/features/AyahDisplay";
import PrayerTimes from "./components/features/PrayerTimes";
import DailyHadith from "./components/features/DailyHadith";
import IslamicQuotes from "./components/features/IslamicQuotes";
import DhikrCounter from "./components/features/DhikrCounter";
import { motion } from "framer-motion";

function App() {
  return (
    <ThemeProvider>
      <MoodProvider>
        <Layout>
          <div className="space-y-8">
            {/* Hero Section with Mood Selector */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center py-8"
            >
              <MoodSelector />
            </motion.section>

            {/* Ayah Display */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <AyahDisplay />
            </motion.section>

            {/* Islamic Features Grid */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
            >
              <PrayerTimes />
              <DailyHadith />
              <IslamicQuotes />
            </motion.section>

            {/* Additional Features */}
            <motion.section
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <DhikrCounter />
              {/* Space for future features */}
              <div className="glass-effect rounded-2xl p-6 border border-white/20 shadow-xl flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-emerald-500 to-blue-500 flex items-center justify-center">
                    <span className="text-2xl">🕌</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    More Features Coming Soon
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Qibla finder, Islamic calendar, and more spiritual tools
                  </p>
                </div>
              </div>
            </motion.section>

            {/* Footer */}
            <motion.footer
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-center py-8 text-gray-600 dark:text-gray-400"
            >
              <p className="mb-2">
                May Allah guide us all to the straight path
              </p>
              <p className="text-sm">Built with ❤️ for the Muslim community</p>
            </motion.footer>
          </div>
        </Layout>
      </MoodProvider>
    </ThemeProvider>
  );
}

export default App;
