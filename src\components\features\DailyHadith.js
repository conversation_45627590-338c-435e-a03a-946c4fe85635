import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { BookOpen, Calendar, Share2, RefreshCw } from "lucide-react";
import { cn } from "../../utils/cn";

const DailyHadith = () => {
  const [currentHadith, setCurrentHadith] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Collection of authentic Hadiths
  const hadithCollection = [
    {
      id: 1,
      arabic:
        "إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى",
      english:
        "Actions are but by intention and every man shall have only that which he intended.",
      narrator: "<PERSON><PERSON> ibn <PERSON>",
      source: "<PERSON><PERSON><PERSON> <PERSON><PERSON>",
      reference: "Hadith 1",
      theme: "Intention",
      category: "Faith",
    },
    {
      id: 2,
      arabic:
        "مَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الْآخِرِ فَلْيَقُلْ خَيْرًا أَوْ لِيَصْمُتْ",
      english:
        "Whoever believes in Allah and the Last Day should speak good or remain silent.",
      narrator: "Abu Hurairah",
      source: "<PERSON><PERSON><PERSON> <PERSON>",
      reference: "Hadith 6018",
      theme: "Speech",
      category: "Manners",
    },
    {
      id: 3,
      arabic: "الْمُسْلِمُ مَنْ سَلِمَ الْمُسْلِمُونَ مِنْ لِسَانِهِ وَيَدِهِ",
      english:
        "A Muslim is one from whose tongue and hand the Muslims are safe.",
      narrator: "Abdullah ibn Amr",
      source: "Sahih al-Bukhari",
      reference: "Hadith 10",
      theme: "Character",
      category: "Faith",
    },
    {
      id: 4,
      arabic:
        "لَا يُؤْمِنُ أَحَدُكُمْ حَتَّى يُحِبَّ لِأَخِيهِ مَا يُحِبُّ لِنَفْسِهِ",
      english:
        "None of you truly believes until he loves for his brother what he loves for himself.",
      narrator: "Anas ibn Malik",
      source: "Sahih al-Bukhari",
      reference: "Hadith 13",
      theme: "Brotherhood",
      category: "Faith",
    },
    {
      id: 5,
      arabic: "مَنْ لَا يَرْحَمُ النَّاسَ لَا يَرْحَمُهُ اللَّهُ",
      english:
        "He who does not show mercy to people, Allah will not show mercy to him.",
      narrator: "Jarir ibn Abdullah",
      source: "Sahih al-Bukhari",
      reference: "Hadith 7376",
      theme: "Mercy",
      category: "Character",
    },
    {
      id: 6,
      arabic: "الدِّينُ النَّصِيحَةُ",
      english: "Religion is sincere advice.",
      narrator: "Tamim ad-Dari",
      source: "Sahih Muslim",
      reference: "Hadith 55",
      theme: "Sincerity",
      category: "Faith",
    },
    {
      id: 7,
      arabic:
        "مَنْ نَفَّسَ عَنْ مُؤْمِنٍ كُرْبَةً مِنْ كُرَبِ الدُّنْيَا نَفَّسَ اللَّهُ عَنْهُ كُرْبَةً مِنْ كُرَبِ يَوْمِ الْقِيَامَةِ",
      english:
        "Whoever relieves a believer of distress in this world, Allah will relieve him of distress on the Day of Resurrection.",
      narrator: "Abu Hurairah",
      source: "Sahih Muslim",
      reference: "Hadith 2699",
      theme: "Helping Others",
      category: "Character",
    },
    {
      id: 8,
      arabic: "خَيْرُ النَّاسِ أَنْفَعُهُمْ لِلنَّاسِ",
      english:
        "The best of people are those who are most beneficial to others.",
      narrator: "Jabir ibn Abdullah",
      source: "Al-Mu'jam al-Awsat",
      reference: "Hadith 5787",
      theme: "Benefiting Others",
      category: "Character",
    },
    {
      id: 9,
      arabic: "تَبَسُّمُكَ فِي وَجْهِ أَخِيكَ صَدَقَةٌ",
      english: "Your smile in the face of your brother is charity.",
      narrator: "Abu Dharr",
      source: "Sunan at-Tirmidhi",
      reference: "Hadith 1956",
      theme: "Kindness",
      category: "Manners",
    },
    {
      id: 10,
      arabic: "مَنْ لَمْ يَشْكُرِ النَّاسَ لَمْ يَشْكُرِ اللَّهَ",
      english: "Whoever does not thank people has not thanked Allah.",
      narrator: "Abu Hurairah",
      source: "Sunan at-Tirmidhi",
      reference: "Hadith 1954",
      theme: "Gratitude",
      category: "Character",
    },
  ];

  useEffect(() => {
    loadDailyHadith();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const loadDailyHadith = () => {
    setIsLoading(true);

    // Get hadith based on current date for consistency
    const today = new Date();
    const dayOfYear = Math.floor(
      (today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24
    );
    const hadithIndex = dayOfYear % hadithCollection.length;

    setTimeout(() => {
      setCurrentHadith(hadithCollection[hadithIndex]);
      setIsLoading(false);
    }, 500);
  };

  const handleShare = async () => {
    if (currentHadith && navigator.share) {
      try {
        await navigator.share({
          title: "Daily Hadith",
          text: `${currentHadith.english}\n\nNarrated by ${currentHadith.narrator}\n${currentHadith.source}`,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(
          `${currentHadith.english}\n\nNarrated by ${currentHadith.narrator}\n${currentHadith.source}`
        );
      }
    }
  };

  if (isLoading) {
    return (
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-300 rounded"></div>
            <div className="h-3 bg-gray-300 rounded w-5/6"></div>
            <div className="h-3 bg-gray-300 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass-effect rounded-2xl p-6 border border-white/20 shadow-xl"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
            <BookOpen className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
              Daily Hadith
            </h3>
            <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
              <Calendar className="w-3 h-3" />
              <span>{new Date().toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <motion.button
            onClick={handleShare}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 rounded-full bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 transition-colors"
          >
            <Share2 className="w-4 h-4" />
          </motion.button>

          <motion.button
            onClick={loadDailyHadith}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 rounded-full bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {currentHadith && (
        <div className="space-y-4">
          {/* Arabic Text */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <p className="text-xl font-arabic text-right leading-relaxed text-gray-800 dark:text-gray-200">
              {currentHadith.arabic}
            </p>
          </motion.div>

          {/* English Translation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300 italic">
              "{currentHadith.english}"
            </p>
          </motion.div>

          {/* Attribution */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="pt-4 border-t border-gray-200/50 dark:border-gray-700/50"
          >
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Narrated by {currentHadith.narrator}
                </p>
                <p className="text-xs text-gray-500">
                  {currentHadith.source} - {currentHadith.reference}
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <span
                  className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium",
                    "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                  )}
                >
                  {currentHadith.theme}
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </motion.div>
  );
};

export default DailyHadith;
