import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Clock, MapPin, Compass } from "lucide-react";
import {
  formatPrayerTime,
  getCurrentPrayer,
  getNextPrayer,
} from "../../utils/islamicUtils";
import { cn } from "../../utils/cn";

const PrayerTimes = () => {
  const [prayerTimes, setPrayerTimes] = useState(null);
  const [location, setLocation] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(true);

  // Mock prayer times - in production, use a real API like Aladhan API
  const mockPrayerTimes = {
    fajr: "05:30",
    dhuhr: "12:15",
    asr: "15:45",
    maghrib: "18:20",
    isha: "19:45",
  };

  useEffect(() => {
    // Get user location and fetch prayer times
    const fetchPrayerTimes = async () => {
      try {
        // Mock location for demo
        setLocation({ city: "Mecca", country: "Saudi Arabia" });
        setPrayerTimes(mockPrayerTimes);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching prayer times:", error);
        setLoading(false);
      }
    };

    fetchPrayerTimes();

    // Update current time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const prayers = [
    { name: "Fajr", arabic: "الفجر", key: "fajr", icon: "🌅" },
    { name: "Dhuhr", arabic: "الظهر", key: "dhuhr", icon: "☀️" },
    { name: "Asr", arabic: "العصر", key: "asr", icon: "🌤️" },
    { name: "Maghrib", arabic: "المغرب", key: "maghrib", icon: "🌅" },
    { name: "Isha", arabic: "العشاء", key: "isha", icon: "🌙" },
  ];

  const currentPrayer = getCurrentPrayer(prayerTimes);
  const nextPrayer = getNextPrayer(prayerTimes);

  if (loading) {
    return (
      <div className="glass-effect rounded-2xl p-6 border border-white/20">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-3 bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass-effect rounded-2xl p-6 border border-white/20 shadow-xl"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-emerald-500 to-blue-500 flex items-center justify-center">
            <Clock className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
              Prayer Times
            </h3>
            <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
              <MapPin className="w-3 h-3" />
              <span>
                {location?.city}, {location?.country}
              </span>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {currentTime.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
          <div className="text-xs text-gray-500">
            {currentTime.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Current/Next Prayer Highlight */}
      {nextPrayer && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-4 p-3 rounded-xl bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-500/30"
        >
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Next Prayer:
              </span>
              <div className="font-semibold text-gray-800 dark:text-gray-200">
                {nextPrayer.name} at {formatPrayerTime(nextPrayer.time)}
              </div>
            </div>
            <Compass className="w-5 h-5 text-emerald-500" />
          </div>
        </motion.div>
      )}

      {/* Prayer Times List */}
      <div className="space-y-2">
        {prayers.map((prayer, index) => {
          const time = prayerTimes?.[prayer.key];
          const isCurrent = currentPrayer === prayer.name;

          return (
            <motion.div
              key={prayer.key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg transition-all",
                isCurrent
                  ? "bg-emerald-500/20 border border-emerald-500/30"
                  : "hover:bg-white/10"
              )}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{prayer.icon}</span>
                <div>
                  <div
                    className={cn(
                      "font-medium",
                      isCurrent
                        ? "text-emerald-600 dark:text-emerald-400"
                        : "text-gray-800 dark:text-gray-200"
                    )}
                  >
                    {prayer.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                    {prayer.arabic}
                  </div>
                </div>
              </div>

              <div
                className={cn(
                  "font-mono text-lg",
                  isCurrent
                    ? "text-emerald-600 dark:text-emerald-400 font-bold"
                    : "text-gray-700 dark:text-gray-300"
                )}
              >
                {formatPrayerTime(time)}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Qibla Direction */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50"
      >
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <Compass className="w-4 h-4" />
          <span>Qibla Direction: 245° SW</span>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PrayerTimes;
