@import url("https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap");

/* Basic Reset and Typography */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

body {
  margin: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%);
  min-height: 100vh;
  color: #1f2937;
}

body.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #f1f5f9;
}

body.black {
  background: #000000;
  color: #ffffff;
  position: relative;
  overflow-x: hidden;
}

code {
  font-family: source-code-pro, <PERSON>lo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Utility Classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.islamic-gradient {
  background: linear-gradient(90deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
}

.text-gradient {
  background: linear-gradient(90deg, #059669 0%, #2563eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Basic Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Simple Grid */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 1024px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Spacing */
.space-y-8 > * + * {
  margin-top: 2rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

/* Padding and Margin */
.p-8 {
  padding: 2rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-4 {
  padding: 1rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-2 {
  padding: 0.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.pt-20 {
  padding-top: 5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}

.mt-6 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 1rem;
}

/* Text */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}
.font-semibold {
  font-weight: 600;
}
.font-medium {
  font-weight: 500;
}

.font-arabic {
  font-family: "Amiri", serif;
}

.italic {
  font-style: italic;
}

/* Colors */
.text-gray-800 {
  color: #1f2937;
}
.text-gray-700 {
  color: #374151;
}
.text-gray-600 {
  color: #4b5563;
}
.text-gray-500 {
  color: #6b7280;
}
.text-gray-400 {
  color: #9ca3af;
}

.dark .text-gray-200 {
  color: #e5e7eb;
}
.dark .text-gray-300 {
  color: #d1d5db;
}
.dark .text-gray-400 {
  color: #9ca3af;
}

/* Layout */
.fixed {
  position: fixed;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}

.top-0 {
  top: 0;
}
.left-0 {
  left: 0;
}
.right-0 {
  right: 0;
}
.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}
.z-10 {
  z-index: 10;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}
.w-10 {
  width: 2.5rem;
}
.w-5 {
  width: 1.25rem;
}

.h-64 {
  height: 16rem;
}
.h-10 {
  height: 2.5rem;
}
.h-5 {
  height: 1.25rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Flexbox */
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}

/* Border */
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-full {
  border-radius: 9999px;
}

.border {
  border-width: 1px;
}
.border-t {
  border-top-width: 1px;
}

/* Overflow */
.overflow-hidden {
  overflow: hidden;
}

/* Animations */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}
.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Modern Buttons and 3D Utilities */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.65rem 1rem;
  border-radius: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
  will-change: transform;
}

.btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.25);
}

.btn-primary {
  color: #fff;
  background: linear-gradient(135deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(59, 130, 246, 0.25);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-ghost {
  color: #374151;
  background: rgba(255, 255, 255, 0.6);
  border-color: rgba(0, 0, 0, 0.06);
}

.dark .btn-ghost {
  color: #e5e7eb;
  background: rgba(31, 41, 55, 0.6);
  border-color: rgba(255, 255, 255, 0.08);
}

.btn-ghost:hover {
  transform: translateY(-1px);
}

.btn-icon {
  width: 2.75rem;
  height: 2.75rem;
  padding: 0.5rem;
  border-radius: 9999px;
}

.btn-3d {
  transform-style: preserve-3d;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12),
    0 6px 8px -6px rgba(0, 0, 0, 0.2) inset;
}

.btn-3d:hover {
  transform: translateY(-2px) scale(1.02);
}

.btn-3d:active {
  transform: translateY(0) scale(0.98);
}

.has-shine {
  position: relative;
  overflow: hidden;
}

.has-shine::before {
  content: "";
  position: absolute;
  top: -150%;
  left: -50%;
  width: 200%;
  height: 300%;
  background: linear-gradient(
    120deg,
    transparent 30%,
    rgba(255, 255, 255, 0.25) 50%,
    transparent 70%
  );
  transform: rotate(25deg);
  transition: transform 0.6s ease;
}

.has-shine:hover::before {
  transform: translateX(60%) rotate(25deg);
}

.perspective-1000 {
  perspective: 1000px;
}
.preserve-3d {
  transform-style: preserve-3d;
}

.emoji-3d {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.75)
  );
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.emoji-shadow {
  position: absolute;
  bottom: -6px;
  left: 10%;
  right: 10%;
  height: 12px;
  border-radius: 9999px;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.25) 0%,
    rgba(0, 0, 0, 0.05) 60%,
    transparent 80%
  );
  filter: blur(2px);
}
