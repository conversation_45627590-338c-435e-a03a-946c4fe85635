import {
  getRandomAyah,
  getAyatByMood,
  getAyatByCategory,
  getCategoriesForMood,
  searchAyat,
  getDailyAyah,
  getAyatStats
} from '../index';
import { MOODS } from '../../../contexts/MoodContext';

describe('Ayat Manager', () => {
  describe('getRandomAyah', () => {
    test('returns a random ayah for valid mood', () => {
      const ayah = getRandomAyah(MOODS.HAPPY);
      expect(ayah).toBeDefined();
      expect(ayah).toHaveProperty('id');
      expect(ayah).toHaveProperty('arabic');
      expect(ayah).toHaveProperty('english');
      expect(ayah).toHaveProperty('reference');
    });

    test('returns null for invalid mood', () => {
      const ayah = getRandomAyah('invalid_mood');
      expect(ayah).toBeNull();
    });
  });

  describe('getAyatByMood', () => {
    test('returns array of ayat for valid mood', () => {
      const ayat = getAyatByMood(MOODS.SAD);
      expect(Array.isArray(ayat)).toBe(true);
      expect(ayat.length).toBeGreaterThan(0);
      
      // Check that all ayat have required properties
      ayat.forEach(ayah => {
        expect(ayah).toHaveProperty('id');
        expect(ayah).toHaveProperty('arabic');
        expect(ayah).toHaveProperty('english');
        expect(ayah).toHaveProperty('reference');
        expect(ayah).toHaveProperty('theme');
        expect(ayah).toHaveProperty('category');
      });
    });

    test('returns empty array for invalid mood', () => {
      const ayat = getAyatByMood('invalid_mood');
      expect(Array.isArray(ayat)).toBe(true);
      expect(ayat.length).toBe(0);
    });
  });

  describe('getAyatByCategory', () => {
    test('filters ayat by category correctly', () => {
      const ayat = getAyatByCategory(MOODS.HAPPY, 'gratitude');
      expect(Array.isArray(ayat)).toBe(true);
      
      // All returned ayat should have the specified category
      ayat.forEach(ayah => {
        expect(ayah.category).toBe('gratitude');
      });
    });
  });

  describe('getCategoriesForMood', () => {
    test('returns unique categories for a mood', () => {
      const categories = getCategoriesForMood(MOODS.ANGRY);
      expect(Array.isArray(categories)).toBe(true);
      expect(categories.length).toBeGreaterThan(0);
      
      // Check that all categories are unique
      const uniqueCategories = [...new Set(categories)];
      expect(categories.length).toBe(uniqueCategories.length);
    });
  });

  describe('searchAyat', () => {
    test('searches ayat by content', () => {
      const results = searchAyat('Allah');
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // Check that results contain the search term
      results.forEach(ayah => {
        const containsSearchTerm = 
          ayah.english.toLowerCase().includes('allah') ||
          ayah.theme.toLowerCase().includes('allah') ||
          ayah.category.toLowerCase().includes('allah');
        expect(containsSearchTerm).toBe(true);
      });
    });

    test('returns empty array for non-existent search term', () => {
      const results = searchAyat('nonexistentterm12345');
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(0);
    });
  });

  describe('getDailyAyah', () => {
    test('returns a consistent daily ayah', () => {
      const ayah1 = getDailyAyah();
      const ayah2 = getDailyAyah();
      
      expect(ayah1).toBeDefined();
      expect(ayah2).toBeDefined();
      expect(ayah1.id).toBe(ayah2.id); // Should be the same for the same day
    });
  });

  describe('getAyatStats', () => {
    test('returns correct statistics', () => {
      const stats = getAyatStats();
      
      expect(stats).toHaveProperty('total');
      expect(stats).toHaveProperty('byMood');
      expect(stats).toHaveProperty('byCategory');
      
      expect(typeof stats.total).toBe('number');
      expect(stats.total).toBeGreaterThan(0);
      
      expect(typeof stats.byMood).toBe('object');
      expect(typeof stats.byCategory).toBe('object');
    });
  });
});
