import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Heart, Shuffle, Play, Pause, Volume2 } from "lucide-react";
import { cn } from "../../utils/cn";

const NamesOfAllah = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [favorites, setFavorites] = useState([]);

  // 99 Beautiful Names of Allah (<PERSON>ma ul-Husna)
  const asmaUlHusna = [
    {
      arabic: "الرَّحْمَنُ",
      transliteration: "<PERSON><PERSON><PERSON><PERSON>",
      meaning: "The Most Gracious",
    },
    {
      arabic: "الرَّحِيمُ",
      transliteration: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      meaning: "The Most Merciful",
    },
    { arabic: "الْمَلِكُ", transliteration: "Al-<PERSON>", meaning: "The King" },
    {
      arabic: "الْقُدُّوسُ",
      transliteration: "Al-Q<PERSON><PERSON>",
      meaning: "The Most Holy",
    },
    {
      arabic: "السَّلاَمُ",
      transliteration: "<PERSON>-<PERSON><PERSON><PERSON>",
      meaning: "The Source of Peace",
    },
    {
      arabic: "الْمُؤْمِنُ",
      transliteration: "Al-<PERSON>'<PERSON>",
      meaning: "The Guardian of Faith",
    },
    {
      arabic: "الْمُهَيْمِنُ",
      transliteration: "Al-Muhaymin",
      meaning: "The Protector",
    },
    { arabic: "الْعَزِيزُ", transliteration: "Al-Aziz", meaning: "The Mighty" },
    {
      arabic: "الْجَبَّارُ",
      transliteration: "Al-Jabbar",
      meaning: "The Compeller",
    },
    {
      arabic: "الْمُتَكَبِّرُ",
      transliteration: "Al-Mutakabbir",
      meaning: "The Supreme",
    },
    {
      arabic: "الْخَالِقُ",
      transliteration: "Al-Khaliq",
      meaning: "The Creator",
    },
    {
      arabic: "الْبَارِئُ",
      transliteration: "Al-Bari",
      meaning: "The Originator",
    },
    {
      arabic: "الْمُصَوِّرُ",
      transliteration: "Al-Musawwir",
      meaning: "The Fashioner",
    },
    {
      arabic: "الْغَفَّارُ",
      transliteration: "Al-Ghaffar",
      meaning: "The Repeatedly Forgiving",
    },
    {
      arabic: "الْقَهَّارُ",
      transliteration: "Al-Qahhar",
      meaning: "The Subduer",
    },
    {
      arabic: "الْوَهَّابُ",
      transliteration: "Al-Wahhab",
      meaning: "The Bestower",
    },
    {
      arabic: "الرَّزَّاقُ",
      transliteration: "Ar-Razzaq",
      meaning: "The Provider",
    },
    {
      arabic: "الْفَتَّاحُ",
      transliteration: "Al-Fattah",
      meaning: "The Opener",
    },
    {
      arabic: "الْعَلِيمُ",
      transliteration: "Al-Aleem",
      meaning: "The All-Knowing",
    },
    {
      arabic: "الْقَابِضُ",
      transliteration: "Al-Qabid",
      meaning: "The Constrictor",
    },
    {
      arabic: "الْبَاسِطُ",
      transliteration: "Al-Basit",
      meaning: "The Expander",
    },
    {
      arabic: "الْخَافِضُ",
      transliteration: "Al-Khafid",
      meaning: "The Abaser",
    },
    {
      arabic: "الرَّافِعُ",
      transliteration: "Ar-Rafi",
      meaning: "The Exalter",
    },
    {
      arabic: "الْمُعِزُّ",
      transliteration: "Al-Mu'izz",
      meaning: "The Honorer",
    },
    {
      arabic: "الْمُذِلُّ",
      transliteration: "Al-Mudhill",
      meaning: "The Humiliator",
    },
    {
      arabic: "السَّمِيعُ",
      transliteration: "As-Samee",
      meaning: "The All-Hearing",
    },
    {
      arabic: "الْبَصِيرُ",
      transliteration: "Al-Baseer",
      meaning: "The All-Seeing",
    },
    { arabic: "الْحَكَمُ", transliteration: "Al-Hakam", meaning: "The Judge" },
    { arabic: "الْعَدْلُ", transliteration: "Al-Adl", meaning: "The Just" },
    {
      arabic: "اللَّطِيفُ",
      transliteration: "Al-Lateef",
      meaning: "The Gentle",
    },
    {
      arabic: "الْخَبِيرُ",
      transliteration: "Al-Khabeer",
      meaning: "The Aware",
    },
    {
      arabic: "الْحَلِيمُ",
      transliteration: "Al-Haleem",
      meaning: "The Forbearing",
    },
    {
      arabic: "الْعَظِيمُ",
      transliteration: "Al-Azeem",
      meaning: "The Magnificent",
    },
    {
      arabic: "الْغَفُورُ",
      transliteration: "Al-Ghafoor",
      meaning: "The Forgiving",
    },
    {
      arabic: "الشَّكُورُ",
      transliteration: "Ash-Shakoor",
      meaning: "The Appreciative",
    },
    {
      arabic: "الْعَلِيُّ",
      transliteration: "Al-Ali",
      meaning: "The Most High",
    },
    {
      arabic: "الْكَبِيرُ",
      transliteration: "Al-Kabeer",
      meaning: "The Greatest",
    },
    {
      arabic: "الْحَفِيظُ",
      transliteration: "Al-Hafeedh",
      meaning: "The Preserver",
    },
    {
      arabic: "الْمُقِيتُ",
      transliteration: "Al-Muqeet",
      meaning: "The Nourisher",
    },
    {
      arabic: "الْحَسِيبُ",
      transliteration: "Al-Haseeb",
      meaning: "The Reckoner",
    },
    {
      arabic: "الْجَلِيلُ",
      transliteration: "Al-Jaleel",
      meaning: "The Majestic",
    },
    {
      arabic: "الْكَرِيمُ",
      transliteration: "Al-Kareem",
      meaning: "The Generous",
    },
    {
      arabic: "الرَّقِيبُ",
      transliteration: "Ar-Raqeeb",
      meaning: "The Watchful",
    },
    {
      arabic: "الْمُجِيبُ",
      transliteration: "Al-Mujeeb",
      meaning: "The Responsive",
    },
    {
      arabic: "الْوَاسِعُ",
      transliteration: "Al-Wasi",
      meaning: "The All-Encompassing",
    },
    { arabic: "الْحَكِيمُ", transliteration: "Al-Hakeem", meaning: "The Wise" },
    {
      arabic: "الْوَدُودُ",
      transliteration: "Al-Wadood",
      meaning: "The Loving",
    },
    {
      arabic: "الْمَجِيدُ",
      transliteration: "Al-Majeed",
      meaning: "The Glorious",
    },
    {
      arabic: "الْبَاعِثُ",
      transliteration: "Al-Ba'ith",
      meaning: "The Resurrector",
    },
    {
      arabic: "الشَّهِيدُ",
      transliteration: "Ash-Shaheed",
      meaning: "The Witness",
    },
    { arabic: "الْحَقُّ", transliteration: "Al-Haqq", meaning: "The Truth" },
    {
      arabic: "الْوَكِيلُ",
      transliteration: "Al-Wakeel",
      meaning: "The Trustee",
    },
    {
      arabic: "الْقَوِيُّ",
      transliteration: "Al-Qawiyy",
      meaning: "The Strong",
    },
    { arabic: "الْمَتِينُ", transliteration: "Al-Mateen", meaning: "The Firm" },
    {
      arabic: "الْوَلِيُّ",
      transliteration: "Al-Waliyy",
      meaning: "The Friend",
    },
    {
      arabic: "الْحَمِيدُ",
      transliteration: "Al-Hameed",
      meaning: "The Praiseworthy",
    },
    {
      arabic: "الْمُحْصِي",
      transliteration: "Al-Muhsee",
      meaning: "The Counter",
    },
    {
      arabic: "الْمُبْدِئُ",
      transliteration: "Al-Mubdi",
      meaning: "The Originator",
    },
    {
      arabic: "الْمُعِيدُ",
      transliteration: "Al-Mu'eed",
      meaning: "The Restorer",
    },
    {
      arabic: "الْمُحْيِي",
      transliteration: "Al-Muhyee",
      meaning: "The Giver of Life",
    },
    {
      arabic: "الْمُمِيتُ",
      transliteration: "Al-Mumeet",
      meaning: "The Taker of Life",
    },
    {
      arabic: "الْحَيُّ",
      transliteration: "Al-Hayy",
      meaning: "The Ever-Living",
    },
    {
      arabic: "الْقَيُّومُ",
      transliteration: "Al-Qayyoom",
      meaning: "The Self-Existing",
    },
    {
      arabic: "الْوَاجِدُ",
      transliteration: "Al-Wajid",
      meaning: "The Finder",
    },
    { arabic: "الْمَاجِدُ", transliteration: "Al-Majid", meaning: "The Noble" },
    { arabic: "الْوَاحِدُ", transliteration: "Al-Wahid", meaning: "The One" },
    { arabic: "الأَحَدُ", transliteration: "Al-Ahad", meaning: "The Unique" },
    {
      arabic: "الصَّمَدُ",
      transliteration: "As-Samad",
      meaning: "The Eternal",
    },
    {
      arabic: "الْقَادِرُ",
      transliteration: "Al-Qadir",
      meaning: "The Capable",
    },
    {
      arabic: "الْمُقْتَدِرُ",
      transliteration: "Al-Muqtadir",
      meaning: "The Powerful",
    },
    {
      arabic: "الْمُقَدِّمُ",
      transliteration: "Al-Muqaddim",
      meaning: "The Expediter",
    },
    {
      arabic: "الْمُؤَخِّرُ",
      transliteration: "Al-Mu'akhkhir",
      meaning: "The Delayer",
    },
    { arabic: "الأَوَّلُ", transliteration: "Al-Awwal", meaning: "The First" },
    { arabic: "الآخِرُ", transliteration: "Al-Akhir", meaning: "The Last" },
    {
      arabic: "الظَّاهِرُ",
      transliteration: "Az-Zahir",
      meaning: "The Manifest",
    },
    {
      arabic: "الْبَاطِنُ",
      transliteration: "Al-Batin",
      meaning: "The Hidden",
    },
    {
      arabic: "الْوَالِي",
      transliteration: "Al-Wali",
      meaning: "The Governor",
    },
    {
      arabic: "الْمُتَعَالِي",
      transliteration: "Al-Muta'ali",
      meaning: "The Most Exalted",
    },
    {
      arabic: "الْبَرُّ",
      transliteration: "Al-Barr",
      meaning: "The Source of Goodness",
    },
    {
      arabic: "التَّوَّابُ",
      transliteration: "At-Tawwab",
      meaning: "The Acceptor of Repentance",
    },
    {
      arabic: "الْمُنْتَقِمُ",
      transliteration: "Al-Muntaqim",
      meaning: "The Avenger",
    },
    {
      arabic: "العَفُوُّ",
      transliteration: "Al-Afuww",
      meaning: "The Pardoner",
    },
    {
      arabic: "الرَّؤُوفُ",
      transliteration: "Ar-Ra'oof",
      meaning: "The Compassionate",
    },
    {
      arabic: "مَالِكُ الْمُلْكِ",
      transliteration: "Malik-ul-Mulk",
      meaning: "Master of the Kingdom",
    },
    {
      arabic: "ذُوالْجَلاَلِ وَالإِكْرَامِ",
      transliteration: "Dhul-Jalali wal-Ikram",
      meaning: "Lord of Majesty and Bounty",
    },
    {
      arabic: "الْمُقْسِطُ",
      transliteration: "Al-Muqsit",
      meaning: "The Equitable",
    },
    {
      arabic: "الْجَامِعُ",
      transliteration: "Al-Jami",
      meaning: "The Gatherer",
    },
    {
      arabic: "الْغَنِيُّ",
      transliteration: "Al-Ghaniyy",
      meaning: "The Independent",
    },
    {
      arabic: "الْمُغْنِي",
      transliteration: "Al-Mughni",
      meaning: "The Enricher",
    },
    {
      arabic: "الْمَانِعُ",
      transliteration: "Al-Mani",
      meaning: "The Preventer",
    },
    {
      arabic: "الضَّارُّ",
      transliteration: "Ad-Darr",
      meaning: "The Distresser",
    },
    {
      arabic: "النَّافِعُ",
      transliteration: "An-Nafi",
      meaning: "The Benefactor",
    },
    { arabic: "النُّورُ", transliteration: "An-Nur", meaning: "The Light" },
    { arabic: "الْهَادِي", transliteration: "Al-Hadi", meaning: "The Guide" },
    {
      arabic: "الْبَدِيعُ",
      transliteration: "Al-Badi",
      meaning: "The Incomparable",
    },
    {
      arabic: "الْبَاقِي",
      transliteration: "Al-Baqi",
      meaning: "The Everlasting",
    },
    {
      arabic: "الْوَارِثُ",
      transliteration: "Al-Warith",
      meaning: "The Inheritor",
    },
    {
      arabic: "الرَّشِيدُ",
      transliteration: "Ar-Rasheed",
      meaning: "The Guide to Right Path",
    },
    {
      arabic: "الصَّبُورُ",
      transliteration: "As-Saboor",
      meaning: "The Patient",
    },
  ];

  useEffect(() => {
    const saved = localStorage.getItem("allah-names-favorites");
    if (saved) {
      setFavorites(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    let interval;
    if (isAutoPlay) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % asmaUlHusna.length);
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [isAutoPlay, asmaUlHusna.length]);

  const toggleFavorite = (index) => {
    const newFavorites = favorites.includes(index)
      ? favorites.filter((fav) => fav !== index)
      : [...favorites, index];

    setFavorites(newFavorites);
    localStorage.setItem("allah-names-favorites", JSON.stringify(newFavorites));
  };

  const getRandomName = () => {
    const randomIndex = Math.floor(Math.random() * asmaUlHusna.length);
    setCurrentIndex(randomIndex);
  };

  const currentName = asmaUlHusna[currentIndex];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/10 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20 dark:border-gray-700/50"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <motion.div
          className="flex items-center space-x-2"
          whileHover={{ scale: 1.05 }}
        >
          <span className="text-2xl">🕌</span>
          <h3 className="text-xl font-bold text-gray-800 dark:text-white">
            Asma ul-Husna
          </h3>
        </motion.div>

        <div className="flex items-center space-x-2">
          <motion.button
            onClick={() => setIsAutoPlay(!isAutoPlay)}
            className={cn(
              "btn btn-ghost btn-3d btn-icon",
              isAutoPlay ? "bg-emerald-500 text-white" : ""
            )}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isAutoPlay ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4" />
            )}
          </motion.button>

          <motion.button
            onClick={getRandomName}
            className="btn btn-ghost btn-3d btn-icon"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Shuffle className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* Current Name Display */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
          animate={{ opacity: 1, scale: 1, rotateY: 0 }}
          exit={{ opacity: 0, scale: 0.8, rotateY: -90 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6 p-6 rounded-xl bg-gradient-to-br from-emerald-500/20 to-blue-500/20 border border-emerald-200/30 dark:border-emerald-700/30"
        >
          {/* Arabic Name */}
          <motion.div
            className="text-4xl md:text-5xl font-arabic text-emerald-600 dark:text-emerald-400 mb-3"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            {currentName.arabic}
          </motion.div>

          {/* Transliteration */}
          <div className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
            {currentName.transliteration}
          </div>

          {/* Meaning */}
          <div className="text-lg text-gray-600 dark:text-gray-300 italic mb-4">
            "{currentName.meaning}"
          </div>

          {/* Name Number */}
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {currentIndex + 1} of 99
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <motion.button
            onClick={() =>
              setCurrentIndex(
                (prev) => (prev - 1 + asmaUlHusna.length) % asmaUlHusna.length
              )
            }
            className="btn btn-ghost btn-3d px-4"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Previous
          </motion.button>

          <motion.button
            onClick={() =>
              setCurrentIndex((prev) => (prev + 1) % asmaUlHusna.length)
            }
            className="btn btn-primary btn-3d px-4"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Next
          </motion.button>
        </div>

        <motion.button
          onClick={() => toggleFavorite(currentIndex)}
          className={cn(
            "btn btn-ghost btn-3d btn-icon",
            favorites.includes(currentIndex) && "text-red-500"
          )}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Heart
            className={cn(
              "w-5 h-5",
              favorites.includes(currentIndex) && "fill-current"
            )}
          />
        </motion.button>
      </div>

      {/* Progress Bar */}
      <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <motion.div
          className="h-2 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full"
          initial={{ width: 0 }}
          animate={{
            width: `${((currentIndex + 1) / asmaUlHusna.length) * 100}%`,
          }}
          transition={{ duration: 0.5 }}
        />
      </div>
    </motion.div>
  );
};

export default NamesOfAllah;
