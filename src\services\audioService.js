/**
 * Audio Service for Quran Recitation
 * Provides audio playback functionality for Quranic verses
 */

class AudioService {
  constructor() {
    this.currentAudio = null;
    this.isPlaying = false;
    this.volume = 0.7;
    this.onPlayStateChange = null;
    this.onProgress = null;
    this.onError = null;
  }

  /**
   * Play Quran recitation for a specific verse
   * @param {string} surah - Surah number
   * @param {string} ayah - Ayah number
   * @param {string} reciter - Reciter identifier (default: 'mishary')
   */
  async playVerse(surah, ayah, reciter = 'mishary') {
    try {
      // Stop current audio if playing
      this.stop();

      // Format surah and ayah numbers with leading zeros
      const formattedSurah = surah.toString().padStart(3, '0');
      const formattedAyah = ayah.toString().padStart(3, '0');

      // Construct audio URL (using EveryAyah.com API)
      const audioUrl = `https://everyayah.com/data/${reciter}/${formattedSurah}${formattedAyah}.mp3`;

      // Create new audio instance
      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.volume = this.volume;

      // Set up event listeners
      this.currentAudio.addEventListener('loadstart', () => {
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: true, isPlaying: false });
        }
      });

      this.currentAudio.addEventListener('canplay', () => {
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: false, isPlaying: false });
        }
      });

      this.currentAudio.addEventListener('play', () => {
        this.isPlaying = true;
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: false, isPlaying: true });
        }
      });

      this.currentAudio.addEventListener('pause', () => {
        this.isPlaying = false;
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: false, isPlaying: false });
        }
      });

      this.currentAudio.addEventListener('ended', () => {
        this.isPlaying = false;
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: false, isPlaying: false });
        }
      });

      this.currentAudio.addEventListener('timeupdate', () => {
        if (this.onProgress && this.currentAudio) {
          const progress = (this.currentAudio.currentTime / this.currentAudio.duration) * 100;
          this.onProgress(progress);
        }
      });

      this.currentAudio.addEventListener('error', (e) => {
        console.error('Audio playback error:', e);
        if (this.onError) {
          this.onError('Failed to load audio. Please check your internet connection.');
        }
        if (this.onPlayStateChange) {
          this.onPlayStateChange({ isLoading: false, isPlaying: false, error: true });
        }
      });

      // Start playback
      await this.currentAudio.play();

    } catch (error) {
      console.error('Error playing verse:', error);
      if (this.onError) {
        this.onError('Failed to play audio. Please try again.');
      }
    }
  }

  /**
   * Play a sample recitation (for demonstration)
   */
  async playSample() {
    // Play Al-Fatiha (1:1) as sample
    await this.playVerse(1, 1);
  }

  /**
   * Pause current audio
   */
  pause() {
    if (this.currentAudio && this.isPlaying) {
      this.currentAudio.pause();
    }
  }

  /**
   * Resume current audio
   */
  resume() {
    if (this.currentAudio && !this.isPlaying) {
      this.currentAudio.play().catch(console.error);
    }
  }

  /**
   * Stop current audio
   */
  stop() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
      this.isPlaying = false;
      if (this.onPlayStateChange) {
        this.onPlayStateChange({ isLoading: false, isPlaying: false });
      }
    }
  }

  /**
   * Set volume (0-1)
   */
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.currentAudio) {
      this.currentAudio.volume = this.volume;
    }
  }

  /**
   * Get current volume
   */
  getVolume() {
    return this.volume;
  }

  /**
   * Check if audio is currently playing
   */
  getIsPlaying() {
    return this.isPlaying;
  }

  /**
   * Set callback for play state changes
   */
  setOnPlayStateChange(callback) {
    this.onPlayStateChange = callback;
  }

  /**
   * Set callback for progress updates
   */
  setOnProgress(callback) {
    this.onProgress = callback;
  }

  /**
   * Set callback for errors
   */
  setOnError(callback) {
    this.onError = callback;
  }

  /**
   * Get available reciters
   */
  getReciters() {
    return [
      { id: 'mishary', name: 'Mishary Rashid Al-Afasy', language: 'Arabic' },
      { id: 'husary', name: 'Mahmoud Khalil Al-Husary', language: 'Arabic' },
      { id: 'sudais', name: 'Abdul Rahman Al-Sudais', language: 'Arabic' },
      { id: 'shuraim', name: 'Saud Al-Shuraim', language: 'Arabic' },
      { id: 'maher', name: 'Maher Al-Muaiqly', language: 'Arabic' },
    ];
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.stop();
    this.onPlayStateChange = null;
    this.onProgress = null;
    this.onError = null;
  }
}

// Create singleton instance
const audioService = new AudioService();

export default audioService;

// Named exports for convenience
export { AudioService };
