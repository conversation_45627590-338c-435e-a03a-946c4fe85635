import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Quote, Heart } from 'lucide-react';
import { cn } from '../../utils/cn';

const IslamicQuotes = () => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const quotes = [
    {
      id: 1,
      text: "And it is He who created the heavens and earth in truth. And the day He says, 'Be,' and it is, His word is the truth.",
      source: "Quran 6:73",
      category: "Creation",
      color: "from-blue-500 to-purple-600"
    },
    {
      id: 2,
      text: "The believer is not one who eats his fill while his neighbor goes hungry.",
      source: "<PERSON> (PBUH)",
      category: "Compassion",
      color: "from-emerald-500 to-teal-600"
    },
    {
      id: 3,
      text: "And whoever relies upon Allah - then He is sufficient for him. Indeed, <PERSON> will accomplish His purpose.",
      source: "Quran 65:3",
      category: "Trust",
      color: "from-indigo-500 to-blue-600"
    },
    {
      id: 4,
      text: "The best of people are those who benefit others.",
      source: "Prophet Muhammad (PBUH)",
      category: "Service",
      color: "from-orange-500 to-red-500"
    },
    {
      id: 5,
      text: "And give good tidings to the patient, Who, when disaster strikes them, say, 'Indeed we belong to Allah, and indeed to Him we will return.'",
      source: "Quran 2:155-156",
      category: "Patience",
      color: "from-purple-500 to-pink-600"
    },
    {
      id: 6,
      text: "A person's true wealth is the good he or she does in this world.",
      source: "Prophet Muhammad (PBUH)",
      category: "Wealth",
      color: "from-green-500 to-emerald-600"
    },
    {
      id: 7,
      text: "And it is He who sends down the rain after they had despaired and spreads His mercy.",
      source: "Quran 42:28",
      category: "Mercy",
      color: "from-cyan-500 to-blue-600"
    },
    {
      id: 8,
      text: "The most beloved of people to Allah are those who are most beneficial to people.",
      source: "Prophet Muhammad (PBUH)",
      category: "Love",
      color: "from-rose-500 to-pink-600"
    }
  ];

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, quotes.length]);

  const nextQuote = () => {
    setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length);
  };

  const prevQuote = () => {
    setCurrentQuoteIndex((prev) => (prev - 1 + quotes.length) % quotes.length);
  };

  const goToQuote = (index) => {
    setCurrentQuoteIndex(index);
  };

  const currentQuote = quotes[currentQuoteIndex];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass-effect rounded-2xl p-6 border border-white/20 shadow-xl relative overflow-hidden"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      {/* Background Gradient */}
      <div className={cn(
        "absolute inset-0 opacity-10 bg-gradient-to-br",
        currentQuote.color
      )} />

      {/* Header */}
      <div className="relative z-10 flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-r",
            currentQuote.color
          )}>
            <Quote className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
              Islamic Wisdom
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {currentQuote.category}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <motion.button
            onClick={prevQuote}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
          >
            <ChevronLeft className="w-4 h-4 text-gray-700 dark:text-gray-300" />
          </motion.button>
          
          <motion.button
            onClick={nextQuote}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
          >
            <ChevronRight className="w-4 h-4 text-gray-700 dark:text-gray-300" />
          </motion.button>
        </div>
      </div>

      {/* Quote Content */}
      <div className="relative z-10 min-h-[120px] flex items-center">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuoteIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="w-full"
          >
            <blockquote className="text-lg md:text-xl leading-relaxed text-gray-800 dark:text-gray-200 mb-4 italic">
              "{currentQuote.text}"
            </blockquote>
            
            <div className="flex items-center justify-between">
              <cite className="text-sm font-medium text-gray-600 dark:text-gray-400 not-italic">
                — {currentQuote.source}
              </cite>
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
              >
                <Heart className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </motion.button>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Dots Indicator */}
      <div className="relative z-10 flex items-center justify-center space-x-2 mt-6">
        {quotes.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => goToQuote(index)}
            className={cn(
              "w-2 h-2 rounded-full transition-all duration-300",
              index === currentQuoteIndex
                ? "bg-emerald-500 w-6"
                : "bg-gray-400 hover:bg-gray-500"
            )}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>

      {/* Progress Bar */}
      {isAutoPlaying && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30">
          <motion.div
            className={cn("h-full bg-gradient-to-r", currentQuote.color)}
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 5, ease: "linear" }}
            key={currentQuoteIndex}
          />
        </div>
      )}
    </motion.div>
  );
};

export default IslamicQuotes;
