{"name": "islamic_web", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.7", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.537.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.1", "three": "^0.179.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}