import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, Volume2, VolumeX, Loader2, AlertCircle } from 'lucide-react';
import { cn } from '../../utils/cn';
import audioService from '../../services/audioService';

const AudioPlayer = ({ 
  surah, 
  ayah, 
  reciter = 'mishary',
  className = '',
  showVolumeControl = true,
  autoPlay = false 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState(audioService.getVolume());
  const [isMuted, setIsMuted] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Set up audio service callbacks
    audioService.setOnPlayStateChange(({ isLoading: loading, isPlaying: playing, error: err }) => {
      setIsLoading(loading);
      setIsPlaying(playing);
      if (err) {
        setError('Failed to load audio');
      }
    });

    audioService.setOnProgress((progressValue) => {
      setProgress(progressValue);
    });

    audioService.setOnError((errorMessage) => {
      setError(errorMessage);
      setIsLoading(false);
      setIsPlaying(false);
    });

    // Auto play if requested
    if (autoPlay && surah && ayah) {
      handlePlay();
    }

    return () => {
      // Cleanup
      audioService.setOnPlayStateChange(null);
      audioService.setOnProgress(null);
      audioService.setOnError(null);
    };
  }, [autoPlay, surah, ayah]);

  const handlePlay = async () => {
    if (!surah || !ayah) {
      setError('Verse information not available');
      return;
    }

    setError(null);
    
    if (isPlaying) {
      audioService.pause();
    } else if (audioService.currentAudio && !isPlaying) {
      audioService.resume();
    } else {
      await audioService.playVerse(surah, ayah, reciter);
    }
  };

  const handleStop = () => {
    audioService.stop();
    setProgress(0);
  };

  const handleVolumeChange = (newVolume) => {
    setVolume(newVolume);
    audioService.setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (isMuted) {
      handleVolumeChange(0.7);
      setIsMuted(false);
    } else {
      handleVolumeChange(0);
      setIsMuted(true);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 border border-emerald-200 dark:border-emerald-700",
        className
      )}
    >
      {/* Play/Pause Button */}
      <motion.button
        onClick={handlePlay}
        disabled={isLoading || !!error}
        className={cn(
          "flex items-center justify-center w-10 h-10 rounded-full transition-all",
          isLoading || error
            ? "bg-gray-300 dark:bg-gray-600 cursor-not-allowed"
            : "bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg hover:shadow-xl"
        )}
        whileHover={!isLoading && !error ? { scale: 1.05 } : {}}
        whileTap={!isLoading && !error ? { scale: 0.95 } : {}}
      >
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0, rotate: 0 }}
              animate={{ opacity: 1, rotate: 360 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2, rotate: { duration: 1, repeat: Infinity, ease: "linear" } }}
            >
              <Loader2 className="w-5 h-5" />
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <AlertCircle className="w-5 h-5 text-red-500" />
            </motion.div>
          ) : isPlaying ? (
            <motion.div
              key="pause"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Pause className="w-5 h-5" />
            </motion.div>
          ) : (
            <motion.div
              key="play"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Play className="w-5 h-5 ml-0.5" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Progress Bar */}
      <div className="flex-1">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <motion.div
            className="h-2 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
        
        {/* Status Text */}
        <div className="mt-1">
          <AnimatePresence mode="wait">
            {error ? (
              <motion.p
                key="error-text"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-xs text-red-500"
              >
                {error}
              </motion.p>
            ) : isLoading ? (
              <motion.p
                key="loading-text"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-xs text-gray-500 dark:text-gray-400"
              >
                Loading recitation...
              </motion.p>
            ) : isPlaying ? (
              <motion.p
                key="playing-text"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-xs text-emerald-600 dark:text-emerald-400"
              >
                Playing Quran recitation
              </motion.p>
            ) : (
              <motion.p
                key="ready-text"
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                className="text-xs text-gray-500 dark:text-gray-400"
              >
                Click to listen to recitation
              </motion.p>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Volume Control */}
      {showVolumeControl && (
        <div className="flex items-center space-x-2">
          <motion.button
            onClick={toggleMute}
            className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="w-4 h-4 text-gray-500" />
            ) : (
              <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            )}
          </motion.button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            style={{
              background: `linear-gradient(to right, #10b981 0%, #10b981 ${volume * 100}%, #e5e7eb ${volume * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
      )}
    </motion.div>
  );
};

export default AudioPlayer;
